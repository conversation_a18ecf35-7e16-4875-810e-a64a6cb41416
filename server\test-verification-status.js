const axios = require('axios');

// 測試驗證狀態修復
async function testVerificationStatus() {
  const baseURL = 'http://localhost:5000/api';
  
  console.log('🧪 測試驗證狀態修復...\n');

  try {
    // 1. 清除現有用戶（如果存在）
    console.log('步驟 1: 準備測試環境...');
    
    // 2. 使用 Andy瑄 進行登入
    console.log('\n步驟 2: 執行 LINE 登入...');
    const loginResponse = await axios.post(`${baseURL}/auth/line-login`, {
      accessToken: 'dev_token_andy'
    });
    
    if (loginResponse.status !== 200) {
      throw new Error(`登入失敗: ${loginResponse.data.message}`);
    }
    
    console.log('✅ 登入成功');
    console.log(`用戶: ${loginResponse.data.user.name}`);
    console.log(`是否為新用戶: ${loginResponse.data.isNewUser}`);
    console.log(`手機驗證狀態 (isVerified): ${loginResponse.data.user.isVerified}`);
    console.log(`LINE 驗證狀態 (lineVerified): ${loginResponse.data.user.lineVerified}`);
    
    // 儲存 JWT token
    const jwtToken = loginResponse.data.token;
    
    // 3. 獲取用戶資料來驗證狀態
    console.log('\n步驟 3: 獲取用戶資料...');
    const profileResponse = await axios.get(`${baseURL}/user/profile`, {
      headers: {
        'Authorization': `Bearer ${jwtToken}`
      }
    });
    
    if (profileResponse.status !== 200) {
      throw new Error(`獲取用戶資料失敗: ${profileResponse.data.message}`);
    }
    
    console.log('✅ 用戶資料獲取成功');
    console.log('用戶詳細資料:');
    console.log(`  ID: ${profileResponse.data.id}`);
    console.log(`  姓名: ${profileResponse.data.name}`);
    console.log(`  Email: ${profileResponse.data.email || '無'}`);
    console.log(`  手機: ${profileResponse.data.phone || '無'}`);
    console.log(`  手機驗證 (isVerified): ${profileResponse.data.isVerified}`);
    console.log(`  LINE 驗證 (lineVerified): ${profileResponse.data.lineVerified}`);
    
    // 4. 分析驗證狀態
    console.log('\n步驟 4: 分析驗證狀態...');
    const user = profileResponse.data;
    const isPhoneVerified = !!user.isVerified;
    const isLineVerified = !!user.lineVerified;
    const isFullyVerified = isPhoneVerified || isLineVerified;
    
    console.log('驗證狀態分析:');
    console.log(`  手機驗證: ${isPhoneVerified ? '✅ 已驗證' : '❌ 未驗證'}`);
    console.log(`  LINE 驗證: ${isLineVerified ? '✅ 已驗證' : '❌ 未驗證'}`);
    console.log(`  完全驗證: ${isFullyVerified ? '✅ 已驗證' : '❌ 未驗證'}`);
    
    // 5. 驗證預期結果
    console.log('\n步驟 5: 驗證預期結果...');
    
    if (loginResponse.data.isNewUser) {
      // 新用戶應該：
      // - isVerified: false (手機未驗證)
      // - lineVerified: true (LINE 已驗證)
      // - 整體狀態：已驗證 (因為 LINE 驗證通過)
      
      if (!isPhoneVerified && isLineVerified && isFullyVerified) {
        console.log('✅ 新用戶驗證狀態正確');
        console.log('   - 手機未驗證 ✓');
        console.log('   - LINE 已驗證 ✓');
        console.log('   - 整體已驗證 ✓');
      } else {
        console.log('❌ 新用戶驗證狀態不正確');
        console.log(`   - 手機驗證: ${isPhoneVerified} (應為 false)`);
        console.log(`   - LINE 驗證: ${isLineVerified} (應為 true)`);
        console.log(`   - 整體驗證: ${isFullyVerified} (應為 true)`);
      }
    } else {
      console.log('ℹ️  現有用戶，檢查驗證狀態...');
      if (isLineVerified) {
        console.log('✅ 現有用戶 LINE 驗證狀態正確');
      } else {
        console.log('❌ 現有用戶 LINE 驗證狀態需要更新');
      }
    }
    
    // 6. 測試第二次登入
    console.log('\n步驟 6: 測試重複登入...');
    const secondLoginResponse = await axios.post(`${baseURL}/auth/line-login`, {
      accessToken: 'dev_token_andy'
    });
    
    console.log('✅ 第二次登入成功');
    console.log(`是否為新用戶: ${secondLoginResponse.data.isNewUser} (應為 false)`);
    console.log(`資料是否有異動: ${secondLoginResponse.data.hasChanges}`);
    
    console.log('\n🎉 驗證狀態測試完成！');
    
    // 總結
    console.log('\n📊 測試總結:');
    console.log('✅ LINE 登入功能正常');
    console.log('✅ 用戶資料獲取正常');
    console.log('✅ JWT token 驗證正常');
    console.log(`✅ 驗證狀態: ${isFullyVerified ? '用戶已驗證' : '用戶未驗證'}`);
    
    if (isLineVerified) {
      console.log('✅ LINE 驗證狀態正確設置');
    } else {
      console.log('❌ LINE 驗證狀態需要修復');
    }
    
  } catch (error) {
    console.log('❌ 測試失敗');
    if (error.response) {
      console.log(`狀態碼: ${error.response.status}`);
      console.log(`錯誤訊息: ${error.response.data.message || error.response.data}`);
    } else if (error.request) {
      console.log('❌ 無法連接到伺服器');
      console.log('請確保執行: cd server && npm run dev');
    } else {
      console.log(`錯誤: ${error.message}`);
    }
  }
}

// 檢查伺服器狀態
async function checkServer() {
  try {
    const response = await axios.get('http://localhost:5000/');
    console.log('✅ 伺服器正在運行');
    return true;
  } catch (error) {
    console.log('❌ 伺服器未運行');
    console.log('請先啟動伺服器: cd server && npm run dev');
    return false;
  }
}

// 主函數
async function main() {
  console.log('🔧 驗證狀態修復測試工具\n');
  
  const serverRunning = await checkServer();
  if (!serverRunning) {
    return;
  }
  
  await testVerificationStatus();
}

main().catch(console.error);
