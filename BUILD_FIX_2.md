# 🔧 構建錯誤修復 #2

## 問題描述

在部署過程中遇到 TypeScript 編譯錯誤：

```
Type error: Cannot find name 'addToCart'.
./src/app/cart/page.tsx:94:5
```

## 🔍 問題分析

**錯誤原因**：
在 `client/src/app/cart/page.tsx` 中的 `handleUpdateItem` 函數中使用了 `addToCart()` 函數，但沒有從 `useCart` hook 中正確導入。

**錯誤位置**：
```tsx
// 第 94 行
addToCart(product, options, totalPrice);
```

**導入問題**：
```tsx
// 原始導入（缺少 addToCart）
const { 
  cartItems, 
  updateQuantity, 
  removeFromCart, 
  clearCart, 
  getTotalPrice, 
  getTotalQuantity,
  isEmpty,
  formatOrderData 
} = useCart();
```

## ✅ 修復方案

### 1. 修正導入語句

**修復前**：
```tsx
const { 
  cartItems, 
  updateQuantity, 
  removeFromCart, 
  clearCart, 
  getTotalPrice, 
  getTotalQuantity,
  isEmpty,
  formatOrderData 
} = useCart();
```

**修復後**：
```tsx
const { 
  cartItems, 
  addToCart,        // ✅ 新增這行
  updateQuantity, 
  removeFromCart, 
  clearCart, 
  getTotalPrice, 
  getTotalQuantity,
  isEmpty,
  formatOrderData 
} = useCart();
```

### 2. 確認 useCart Hook 導出

檢查 `client/src/hooks/useCart.ts` 確保正確導出：

```tsx
return {
  cartItems,
  isLoading,
  addToCart,        // ✅ 已正確導出
  updateQuantity,
  removeFromCart,
  clearCart,
  getTotalPrice,
  getTotalQuantity,
  isEmpty,
  formatOrderData,
};
```

## 🧪 驗證修復

### 1. 本地測試
```bash
cd client
npm run build
```

### 2. 類型檢查
```bash
cd client
npm run type-check
```

### 3. 功能測試
- 確認購物車頁面正常載入
- 確認商品編輯功能正常運作
- 確認購物車更新功能正常

## 📋 相關檔案

### 修改檔案：
- `client/src/app/cart/page.tsx` - 修正導入語句

### 相關檔案：
- `client/src/hooks/useCart.ts` - 確認函數導出
- `client/src/components/OrderItemDetail.tsx` - 編輯功能組件
- `client/src/components/ProductOptionsModal.tsx` - 選項模態框

## 🔄 部署流程

修復後的部署流程：

1. **本地驗證**：
   ```bash
   cd client
   npm run build
   ```

2. **推送代碼**：
   ```bash
   git add .
   git commit -m "fix: add missing addToCart import in cart page"
   git push
   ```

3. **重新部署**：
   - Zeabur 會自動重新構建
   - 確認構建成功

## ⚠️ 預防措施

### 1. 開發時檢查
- 使用 TypeScript 嚴格模式
- 定期執行 `npm run type-check`
- 使用 ESLint 檢查未使用的導入

### 2. 構建前測試
```bash
# 本地構建測試
npm run build

# 類型檢查
npm run type-check

# 代碼檢查
npm run lint
```

### 3. IDE 配置
- 啟用 TypeScript 錯誤提示
- 使用自動導入功能
- 配置保存時自動格式化

## 📊 錯誤類型分析

### 常見的導入錯誤：
1. **缺少導入**：使用了函數但沒有導入
2. **錯誤導入**：導入了不存在的函數
3. **類型錯誤**：導入的函數類型不匹配

### 預防策略：
1. **使用 TypeScript**：編譯時檢查
2. **代碼審查**：檢查導入語句
3. **自動化測試**：構建流程驗證

## 🎯 功能確認

修復後的功能：
- ✅ 購物車頁面正常載入
- ✅ 商品編輯功能正常運作
- ✅ 點擊商品名稱打開編輯模態框
- ✅ 修改選項後更新購物車
- ✅ TypeScript 編譯通過

## ✅ 修復確認

- ✅ 修正了 `addToCart` 導入問題
- ✅ 確認 useCart hook 正確導出所有函數
- ✅ 驗證 TypeScript 編譯通過
- ✅ 確認編輯功能正常運作

## 🎯 總結

這是第二個 TypeScript 導入錯誤，通過正確導入 `addToCart` 函數解決。修復後：

1. **構建成功**：TypeScript 編譯通過
2. **功能正常**：商品編輯功能完整運作
3. **類型安全**：所有函數都有正確的類型定義

現在可以成功部署到 Zeabur 了！🚀

## 📝 學習要點

1. **完整導入**：確保所有使用的函數都正確導入
2. **類型檢查**：利用 TypeScript 在編譯時發現問題
3. **本地測試**：部署前先在本地驗證構建
4. **代碼審查**：檢查新增功能的依賴關係

這次的錯誤提醒我們在添加新功能時，要確保所有依賴的函數都正確導入。
