'use client';

import { useState, useEffect } from 'react';

// 購物車商品類型
export interface CartItem {
  id: number;
  productId: number;
  name: string;
  price: number;
  quantity: number;
  image?: string;
  customizations?: {
    sugar?: string;
    ice?: string;
    toppings?: string[];
  };
}

// 購物車狀態管理 Hook
export const useCart = () => {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // 從 localStorage 載入購物車
  useEffect(() => {
    const savedCart = localStorage.getItem('cart');
    if (savedCart) {
      try {
        setCartItems(JSON.parse(savedCart));
      } catch (error) {
        console.error('Failed to load cart from localStorage:', error);
      }
    }
  }, []);

  // 儲存購物車到 localStorage
  useEffect(() => {
    localStorage.setItem('cart', JSON.stringify(cartItems));
  }, [cartItems]);

  // 新增商品到購物車
  const addToCart = (product: any, customizations?: any) => {
    const cartItem: CartItem = {
      id: Date.now(), // 使用時間戳作為唯一 ID
      productId: product.id,
      name: product.name,
      price: product.price,
      quantity: 1,
      image: product.image,
      customizations,
    };

    setCartItems(prev => {
      // 檢查是否已有相同商品和客製化選項
      const existingItemIndex = prev.findIndex(item => 
        item.productId === product.id && 
        JSON.stringify(item.customizations) === JSON.stringify(customizations)
      );

      if (existingItemIndex > -1) {
        // 如果已存在，增加數量
        const updatedItems = [...prev];
        updatedItems[existingItemIndex].quantity += 1;
        return updatedItems;
      } else {
        // 如果不存在，新增商品
        return [...prev, cartItem];
      }
    });
  };

  // 更新商品數量
  const updateQuantity = (itemId: number, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(itemId);
      return;
    }

    setCartItems(prev =>
      prev.map(item =>
        item.id === itemId ? { ...item, quantity } : item
      )
    );
  };

  // 從購物車移除商品
  const removeFromCart = (itemId: number) => {
    setCartItems(prev => prev.filter(item => item.id !== itemId));
  };

  // 清空購物車
  const clearCart = () => {
    setCartItems([]);
  };

  // 計算總價
  const getTotalPrice = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  // 計算總數量
  const getTotalQuantity = () => {
    return cartItems.reduce((total, item) => total + item.quantity, 0);
  };

  // 檢查購物車是否為空
  const isEmpty = cartItems.length === 0;

  // 格式化訂單數據（用於提交到後端）
  const formatOrderData = (orderType: 'immediate' | 'scheduled', scheduledTime?: Date, note?: string) => {
    return {
      items: cartItems.map(item => ({
        productId: item.productId,
        quantity: item.quantity,
        price: item.price,
        customizations: item.customizations || {}
      })),
      totalPrice: getTotalPrice(),
      orderType,
      scheduledTime,
      note
    };
  };

  return {
    cartItems,
    isLoading,
    addToCart,
    updateQuantity,
    removeFromCart,
    clearCart,
    getTotalPrice,
    getTotalQuantity,
    isEmpty,
    formatOrderData,
  };
};

export default useCart;
