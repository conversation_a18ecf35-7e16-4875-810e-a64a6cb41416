module.exports = (sequelize, Sequelize) => {
  const User = sequelize.define('user', {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    lineId: {
      type: Sequelize.STRING,
      unique: true
    },
    name: {
      type: Sequelize.STRING,
      allowNull: false
    },
    phone: {
      type: Sequelize.STRING,
      unique: true,
      allowNull: true
    },
    email: {
      type: Sequelize.STRING,
      allowNull: true
    },
    pictureUrl: {
      type: Sequelize.TEXT,
      allowNull: true
    },
    isVerified: {
      type: Sequelize.BOOLEAN,
      defaultValue: false
    },
    lineVerified: {
      type: Sequelize.BOOLEAN,
      defaultValue: false
    },
    verificationCode: {
      type: Sequelize.STRING
    },
    verificationExpires: {
      type: Sequelize.DATE
    }
  }, {
    timestamps: true
  });

  return User;
};
