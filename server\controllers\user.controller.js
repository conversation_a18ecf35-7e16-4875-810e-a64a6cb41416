const db = require('../models');
const User = db.users;
const Order = db.orders;
const OrderItem = db.orderItems;
const Product = db.products;

// 獲取用戶資料
exports.getUserProfile = async (req, res) => {
  try {
    const userId = req.userId;

    const user = await User.findByPk(userId, {
      attributes: ['id', 'lineId', 'name', 'email', 'pictureUrl', 'phone', 'isVerified', 'createdAt', 'updatedAt']
    });

    if (!user) {
      return res.status(404).json({ message: '用戶不存在' });
    }

    res.status(200).json(user);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: '伺服器錯誤' });
  }
};

// 更新用戶資料
exports.updateUserProfile = async (req, res) => {
  try {
    const userId = req.userId;
    const { name, phone } = req.body;

    const user = await User.findByPk(userId);

    if (!user) {
      return res.status(404).json({ message: '用戶不存在' });
    }

    // 如果更新手機號碼，需要重新驗證
    if (phone && phone !== user.phone) {
      await user.update({
        name,
        phone,
        isVerified: false
      });
    } else {
      await user.update({
        name
      });
    }

    res.status(200).json({
      message: '用戶資料更新成功',
      user: {
        id: user.id,
        lineId: user.lineId,
        name: user.name,
        phone: user.phone,
        isVerified: user.isVerified
      }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: '伺服器錯誤' });
  }
};

// 獲取用戶訂單歷史
exports.getUserOrders = async (req, res) => {
  try {
    const userId = req.userId;

    const orders = await Order.findAll({
      where: { userId },
      include: [
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'name', 'price', 'image']
            }
          ]
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    res.status(200).json(orders);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: '伺服器錯誤' });
  }
};
