# 🔧 JWT 認證問題修復

## 問題描述

您遇到的 401 未授權錯誤是因為前端的 API 攔截器錯誤地使用了 LIFF access token 而不是 JWT token 來進行 API 認證。

## 🔍 問題原因

1. **前端 API 攔截器**：使用 `getAccessToken()` 取得 LIFF access token
2. **後端期望**：JWT token 用於 API 認證
3. **結果**：LIFF access token 無法通過 JWT 驗證，導致 401 錯誤

## ✅ 修復方案

### 1. 新增 JWT Token 管理系統

在 `client/src/utils/api.ts` 中新增：

```typescript
// JWT Token 管理
export const tokenManager = {
  setToken: (token: string) => localStorage.setItem('jwt_token', token),
  getToken: (): string | null => localStorage.getItem('jwt_token'),
  removeToken: () => localStorage.removeItem('jwt_token'),
  hasValidToken: (): boolean => {
    // 檢查 token 格式和過期時間
  }
};
```

### 2. 修改 API 攔截器

```typescript
// 使用 JWT token 而不是 LIFF access token
api.interceptors.request.use((config) => {
  const token = tokenManager.getToken(); // 改為使用 JWT token
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`;
  }
  return config;
});
```

### 3. 登入時儲存 JWT Token

```typescript
// LINE 登入成功後儲存 JWT token
lineLogin: async (accessToken: string) => {
  const response = await publicApi.post('/auth/line-login', { accessToken });
  
  if (response.data.token) {
    tokenManager.setToken(response.data.token); // 儲存 JWT token
  }
  
  return response.data;
}
```

## 🚀 測試方式

### 1. 使用測試頁面

開啟 `client/test-jwt-auth.html` 在瀏覽器中測試：

1. **開發模式登入**：使用 Andy瑄 的模擬資料
2. **檢查 JWT Token**：確認 token 已正確儲存
3. **獲取用戶資料**：測試 `/api/user/profile` 端點
4. **登出測試**：確認 token 被正確清除

### 2. 後端測試

```bash
cd server
node quick-test.js
```

### 3. 前端整合測試

```typescript
import { loginWithBackend } from '@/utils/liff';
import apiService from '@/utils/api';

// 1. 登入
const result = await loginWithBackend(0);
console.log('登入成功:', result);

// 2. 獲取用戶資料
const profile = await apiService.user.getProfile();
console.log('用戶資料:', profile);
```

## 🔄 完整流程

### 開發模式流程：

1. **前端**：調用 `loginWithBackend(0)` 使用 Andy瑄 的模擬資料
2. **前端**：使用 `dev_token_andy` 調用後端 `/auth/line-login`
3. **後端**：檢測到開發模式，使用模擬的 LINE 用戶資料
4. **後端**：生成 JWT token 並返回
5. **前端**：儲存 JWT token 到 localStorage
6. **前端**：後續 API 請求使用 JWT token 進行認證

### 生產模式流程：

1. **前端**：使用真實的 LIFF access token
2. **後端**：調用真實的 LINE API 取得用戶資料
3. **後端**：生成 JWT token 並返回
4. **前端**：儲存 JWT token 並用於後續 API 認證

## 🎯 關鍵修改

### 前端修改：
- ✅ 新增 `tokenManager` 管理 JWT token
- ✅ 修改 API 攔截器使用 JWT token
- ✅ 登入成功後自動儲存 JWT token
- ✅ 登出時清除 JWT token
- ✅ 401 錯誤時自動清除無效 token

### 後端修改：
- ✅ 用戶資料包含新欄位 (email, pictureUrl)
- ✅ 開發模式支援 Andy瑄 的模擬資料
- ✅ 完整的日誌記錄系統

## 🔍 除錯技巧

### 檢查 JWT Token：
```javascript
// 在瀏覽器控制台
const token = localStorage.getItem('jwt_token');
console.log('JWT Token:', token);

// 解析 token payload
if (token) {
  const payload = JSON.parse(atob(token.split('.')[1]));
  console.log('Token Payload:', payload);
}
```

### 檢查 API 請求：
```javascript
// 在瀏覽器開發者工具的 Network 標籤中
// 檢查請求的 Authorization header 是否包含正確的 JWT token
```

## ✅ 驗證清單

- [ ] JWT token 正確儲存到 localStorage
- [ ] API 請求包含正確的 Authorization header
- [ ] `/api/user/profile` 返回 200 而不是 401
- [ ] 用戶資料包含 email 和 pictureUrl
- [ ] 登出後 JWT token 被清除
- [ ] 401 錯誤時自動清除無效 token

現在您的 JWT 認證應該可以正常工作了！🎉
