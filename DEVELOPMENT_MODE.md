# 🔧 開發模式使用說明

## 概述

在開發模式下，系統會使用模擬的 LINE 用戶資料來跳過與 LINE API 的實際通訊，但後續的所有流程（JWT 驗證、資料庫操作、API 連接等）都會走正式的業務邏輯。

## 🎯 主要特色

- ✅ **跳過 LINE API 通訊**：使用預設的模擬資料
- ✅ **完整業務邏輯**：JWT 驗證、資料庫操作、日誌記錄等都正常運作
- ✅ **真實測試環境**：除了 LINE API 外，其他都是真實的系統行為
- ✅ **快速開發**：無需真實的 LINE 環境即可測試完整功能

## 📊 模擬用戶資料

### 主要測試用戶 (Andy瑄)
```
Line ID: Udf723d52cde2495367205e5751fb8c8d
姓名: Andy瑄
頭像: https://profile.line-scdn.net/0hhnplyM1hN0FdFR30vaZJPi1FNCt-ZG5TIyRxIjgdaSI3IyMVI3svdGwRYCQzJ3AUI3V_LzoXa3FRBkAnQ0PLdVolanBhI3UQcHV_pw
Email: <EMAIL>
Access Token: dev_token_andy
```

### 其他測試用戶
- `dev_token_1`: 測試用戶一 (完整資料)
- `dev_token_2`: 測試用戶二 (完整資料)  
- `dev_token_3`: 測試用戶三 (無 email/頭像)

## 🚀 使用方式

### 1. 後端測試

```bash
cd server
node test-dev-login.js
```

這會測試所有模擬用戶的完整登入流程，包括：
- 新用戶註冊
- 現有用戶登入
- 資料異動檢測
- 日誌記錄
- JWT token 生成和驗證

### 2. 前端使用

#### 基本登入 (使用 Andy瑄 的資料)
```typescript
import { loginWithBackend } from '@/utils/liff';

// 使用預設用戶 (Andy瑄)
const result = await loginWithBackend(0);
console.log(result.user.name); // "Andy瑄"
```

#### 手動 API 測試
```bash
# 使用 Andy瑄 的模擬資料登入
curl -X POST http://localhost:5000/api/auth/line-login \
  -H "Content-Type: application/json" \
  -d '{"accessToken": "dev_token_andy"}'
```

### 3. 開發面板

在前端加入開發面板來視覺化測試：

```tsx
import DevModePanel from '@/components/DevModePanel';

function App() {
  return (
    <div>
      {/* 你的應用內容 */}
      <DevModePanel />
    </div>
  );
}
```

## 🔍 測試場景

### 1. 首次登入 (新用戶註冊)
```typescript
const result = await loginWithBackend(0);
console.log(result.isNewUser); // true
console.log(result.user.lineId); // "Udf723d52cde2495367205e5751fb8c8d"
```

### 2. 重複登入 (無資料異動)
```typescript
// 第二次使用相同用戶登入
const result = await loginWithBackend(0);
console.log(result.isNewUser); // false
console.log(result.hasChanges); // false
```

### 3. 查看用戶日誌
```typescript
import apiService from '@/utils/api';

// 需要先登入取得 JWT token
const loginResult = await loginWithBackend(0);
// JWT token 會自動儲存在前端

// 查看日誌
const logs = await apiService.auth.getUserLogs(1, 10);
console.log(logs.logs); // 顯示登入、登出、資料異動日誌
```

## ⚙️ 系統行為

### 開發模式觸發條件
- `NODE_ENV === 'development'`
- Access token 以 `dev_` 開頭

### 正式流程保持不變
1. **JWT 驗證**：正常生成和驗證 JWT token
2. **資料庫操作**：真實的資料庫讀寫
3. **日誌記錄**：完整的用戶行為日誌
4. **API 路由**：所有 API 端點正常運作
5. **中間件**：認證、授權中間件正常執行

### 僅跳過的部分
- 與 LINE Profile API 的 HTTP 請求
- 與 LINE UserInfo API 的 HTTP 請求
- LIFF SDK 的真實初始化

## 🔄 切換到生產模式

1. **環境變數**：設定 `NODE_ENV=production`
2. **Access Token**：使用真實的 LINE access token (不以 `dev_` 開頭)
3. **LIFF 設定**：確保 `NEXT_PUBLIC_LIFF_ID` 正確設定

系統會自動檢測並切換到真實的 LINE API 通訊。

## 🎯 測試檢查清單

- [ ] 後端伺服器啟動成功
- [ ] 資料庫連接正常
- [ ] Andy瑄 用戶首次登入 (新用戶註冊)
- [ ] Andy瑄 用戶重複登入 (無異動)
- [ ] 其他測試用戶登入
- [ ] JWT token 正常生成和驗證
- [ ] 用戶日誌正確記錄
- [ ] 登出功能正常
- [ ] API 路由全部可用

## 💡 開發技巧

### 瀏覽器控制台
```javascript
// 快速切換用戶
window.liffDev.switchUser(0); // Andy瑄

// 執行登入
window.devLogin(0);

// 查看可用用戶
window.liffDev.getAvailableUsers();
```

### 後端日誌監控
後端會顯示開發模式訊息：
```
🔧 Development mode: Using mock LINE user data
🔧 Development mode: Using mock LINE user info
```

這樣您就可以在開發時享受快速的測試環境，同時確保所有業務邏輯都經過完整的驗證！
