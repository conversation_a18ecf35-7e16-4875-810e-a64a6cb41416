'use client';

import ProtectedRoute from '@/components/ProtectedRoute';
import { useLiff } from '@/providers/LiffProvider';
import Link from 'next/link';
import { Coffee, ArrowLeft, ShoppingCart, Star, Plus, Flame, Sparkles } from 'lucide-react';
import { useState, useEffect } from 'react';
import apiService from '@/utils/api';
import { useCart } from '@/hooks/useCart';
import ProductOptionsModal from '@/components/ProductOptionsModal';

export default function Products() {
  const { userProfile } = useLiff();
  const { addToCart, getTotalQuantity } = useCart();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [categories, setCategories] = useState<string[]>([]);
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProduct, setSelectedProduct] = useState<any | null>(null);
  const [isOptionsModalOpen, setIsOptionsModalOpen] = useState(false);

  // 獲取產品分類
  const fetchCategories = async () => {
    try {
      const categoriesData = await apiService.products.getCategories();
      setCategories(['all', ...categoriesData]);
    } catch (err) {
      console.error('Failed to fetch categories:', err);
      setError('無法載入產品分類');
    }
  };

  // 獲取產品列表
  const fetchProducts = async () => {
    try {
      const productsData = await apiService.products.getAll();
      setProducts(productsData);
    } catch (err) {
      console.error('Failed to fetch products:', err);
      setError('無法載入產品列表');
    } finally {
      setLoading(false);
    }
  };

  // 初始化數據
  useEffect(() => {
    const initializeData = async () => {
      setLoading(true);
      await Promise.all([fetchCategories(), fetchProducts()]);
    };

    initializeData();
  }, []);

  // 處理產品點擊（打開選項模態框）
  const handleProductClick = (product: any) => {
    setSelectedProduct(product);
    setIsOptionsModalOpen(true);
  };

  // 處理加入購物車（從選項模態框）
  const handleAddToCartWithOptions = (product: any, options: any, totalPrice: number) => {
    addToCart(product, options, totalPrice);
    setIsOptionsModalOpen(false);
    setSelectedProduct(null);
    // 可以添加成功提示
  };

  // 快速加入購物車（無選項或使用預設選項）
  const handleQuickAddToCart = (product: any) => {
    if (product.options && product.options.length > 0) {
      // 如果有選項，打開選項模態框
      handleProductClick(product);
    } else {
      // 如果沒有選項，直接加入購物車
      addToCart(product);
    }
  };

  const filteredProducts = selectedCategory === 'all'
    ? products
    : products.filter(product => product.category === selectedCategory);

  // 載入中狀態
  if (loading) {
    return (
      <ProtectedRoute requireAuth={true} requireVerification={true}>
        <div className="min-h-screen gradient-bg flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">載入產品中...</p>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  // 錯誤狀態
  if (error) {
    return (
      <ProtectedRoute requireAuth={true} requireVerification={true}>
        <div className="min-h-screen gradient-bg flex items-center justify-center">
          <div className="text-center">
            <Coffee className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <p className="text-red-500 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-3 bg-orange-500 text-white rounded-full hover:bg-orange-600 transition-colors"
            >
              重新載入
            </button>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requireAuth={true} requireVerification={true}>
      <div className="min-h-screen gradient-bg">
        {/* 頂部導航欄 */}
        <header className="bg-white/80 backdrop-blur-md shadow-soft sticky top-0 z-50">
          <div className="container mx-auto px-4 py-4 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Link
                href="/"
                className="p-2 bg-orange-100 text-orange-600 rounded-full hover:bg-orange-200 transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </Link>
              <div className="flex items-center gap-2">
                <Coffee className="w-6 h-6 text-orange-500" />
                <h1 className="text-xl font-bold text-gray-800">點餐</h1>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">
                {userProfile?.displayName}
              </span>
              <Link href="/cart" className="relative">
                <div className="p-2 bg-green-100 text-green-600 rounded-full hover:bg-green-200 transition-colors">
                  <ShoppingCart className="w-5 h-5" />
                </div>
                {getTotalQuantity() > 0 && (
                  <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    {getTotalQuantity()}
                  </div>
                )}
              </Link>
            </div>
          </div>
        </header>

        {/* 主要內容 */}
        <main className="container mx-auto px-4 py-6">
          {/* 歡迎訊息 */}
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm p-6 mb-6">
            <h2 className="text-2xl font-bold text-gray-800 mb-2">
              歡迎點餐！{userProfile?.displayName} 👋
            </h2>
            <p className="text-gray-600">
              選擇您喜愛的飲品，我們為您精心調製
            </p>
          </div>

          {/* 分類選擇 - 固定顯示 */}
          <div className="sticky top-[73px] z-40 bg-gradient-to-b from-orange-50 to-transparent pb-4 mb-6">
            <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-soft">
              <div className="flex gap-2 overflow-x-auto pb-2">
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`px-4 py-2 rounded-full whitespace-nowrap transition-colors shadow-sm ${
                      selectedCategory === category
                        ? 'bg-orange-500 text-white shadow-orange-200'
                        : 'bg-white text-gray-600 hover:bg-orange-100 border border-gray-200'
                    }`}
                  >
                    {category === 'all' ? '全部' : category}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* 產品列表 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pb-32">
            {filteredProducts.map((product) => (
              <div
                key={product.id}
                className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm overflow-hidden hover:shadow-lg transition-shadow relative"
              >
                {/* 產品標示 */}
                <div className="absolute top-3 left-3 z-10 flex gap-2">
                  {product.isPopular && (
                    <div className="flex items-center gap-1 px-2 py-1 bg-red-500 text-white text-xs font-bold rounded-full shadow-lg">
                      <Flame className="w-3 h-3" />
                      熱門
                    </div>
                  )}
                  {product.isNew && (
                    <div className="flex items-center gap-1 px-2 py-1 bg-green-500 text-white text-xs font-bold rounded-full shadow-lg">
                      <Sparkles className="w-3 h-3" />
                      新品
                    </div>
                  )}
                </div>

                <div className="h-48 bg-gradient-to-br from-orange-100 to-orange-200 flex items-center justify-center">
                  {product.image ? (
                    <img
                      src={product.image}
                      alt={product.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <Coffee className="w-16 h-16 text-orange-400" />
                  )}
                </div>
                <div className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-bold text-gray-800">{product.name}</h3>
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="text-sm text-gray-600">4.5</span>
                    </div>
                  </div>
                  <p className="text-gray-600 text-sm mb-3">
                    {product.description || '美味飲品，值得品嚐'}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-xl font-bold text-orange-500">
                      NT$ {product.price}
                    </span>
                    <button
                      onClick={() => handleQuickAddToCart(product)}
                      className="flex items-center gap-2 px-4 py-2 bg-orange-500 text-white rounded-full hover:bg-orange-600 transition-colors"
                    >
                      <Plus className="w-4 h-4" />
                      {product.options && product.options.length > 0 ? '客製化' : '加入購物車'}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredProducts.length === 0 && (
            <div className="text-center py-12">
              <Coffee className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">此分類暫無商品</p>
            </div>
          )}
        </main>

        {/* 底部購物車按鈕 */}
        {getTotalQuantity() > 0 && (
          <div className="fixed bottom-0 left-0 right-0 z-50 p-4 bg-gradient-to-t from-white via-white to-transparent">
            <div className="container mx-auto max-w-md">
              <Link
                href="/cart"
                className="flex items-center justify-between w-full py-4 px-6 gradient-green text-white rounded-2xl shadow-warm btn-hover"
              >
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <ShoppingCart className="w-6 h-6" />
                    <div className="absolute -top-2 -right-2 w-5 h-5 bg-white text-green-600 text-xs rounded-full flex items-center justify-center font-bold">
                      {getTotalQuantity()}
                    </div>
                  </div>
                  <span className="font-semibold">查看購物車</span>
                </div>
                <div className="text-right">
                  <p className="text-sm opacity-90">總計</p>
                  <p className="font-bold text-lg">NT$ {getTotalPrice()}</p>
                </div>
              </Link>
            </div>
          </div>
        )}

        {/* 產品選項模態框 */}
        {selectedProduct && (
          <ProductOptionsModal
            product={selectedProduct}
            isOpen={isOptionsModalOpen}
            onClose={() => {
              setIsOptionsModalOpen(false);
              setSelectedProduct(null);
            }}
            onAddToCart={handleAddToCartWithOptions}
          />
        )}
      </div>
    </ProtectedRoute>
  );
}
