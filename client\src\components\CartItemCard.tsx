'use client';

import { Coffee, Plus, Minus, Trash2 } from 'lucide-react';
import { CartItem } from '@/hooks/useCart';

interface CartItemCardProps {
  item: CartItem;
  onUpdateQuantity: (itemId: number, quantity: number) => void;
  onRemove: (itemId: number) => void;
}

export default function CartItemCard({ item, onUpdateQuantity, onRemove }: CartItemCardProps) {
  // 格式化選項顯示
  const formatCustomizations = () => {
    if (!item.customizations || Object.keys(item.customizations).length === 0) {
      return '標準製作';
    }

    const options = [];

    // 只顯示非預設的選項
    if (item.customizations.sugar && item.customizations.sugar !== '正常糖') {
      options.push(item.customizations.sugar);
    }

    if (item.customizations.ice && item.customizations.ice !== '正常冰') {
      options.push(item.customizations.ice);
    }

    // 配件總是顯示
    if (item.customizations.toppings && item.customizations.toppings.length > 0) {
      options.push(...item.customizations.toppings);
    }

    return options.length > 0 ? options.join(' • ') : '標準製作';
  };

  // 計算加料費用
  const getAddOnPrice = () => {
    return item.totalPrice - item.basePrice;
  };

  return (
    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors">
      {/* 商品圖片 */}
      <div className="w-12 h-12 bg-gradient-to-br from-orange-100 to-orange-200 rounded-lg flex items-center justify-center flex-shrink-0">
        {item.image ? (
          <img
            src={item.image}
            alt={item.name}
            className="w-full h-full object-cover rounded-lg"
          />
        ) : (
          <Coffee className="w-6 h-6 text-orange-400" />
        )}
      </div>

      {/* 商品資訊 */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold text-gray-800 text-sm truncate">{item.name}</h3>
          <p className="font-semibold text-gray-800 text-sm ml-2">
            NT$ {item.totalPrice * item.quantity}
          </p>
        </div>

        <div className="flex items-center justify-between mt-1">
          {/* 價格和選項 */}
          <div className="flex-1 min-w-0">
            <p className="text-xs text-gray-500">
              NT$ {item.totalPrice}
              {getAddOnPrice() > 0 && (
                <span className="text-orange-500 ml-1">(+{getAddOnPrice()})</span>
              )}
            </p>
            <p className="text-xs text-gray-400 truncate" title={formatCustomizations()}>
              {formatCustomizations()}
            </p>
          </div>

          {/* 數量控制和刪除 */}
          <div className="flex items-center gap-2 ml-3">
            <div className="flex items-center gap-1">
              <button
                onClick={() => onUpdateQuantity(item.id, item.quantity - 1)}
                className="p-1 bg-gray-200 rounded-full hover:bg-gray-300 transition-colors"
              >
                <Minus className="w-3 h-3" />
              </button>
              <span className="w-6 text-center font-semibold text-xs">{item.quantity}</span>
              <button
                onClick={() => onUpdateQuantity(item.id, item.quantity + 1)}
                className="p-1 bg-gray-200 rounded-full hover:bg-gray-300 transition-colors"
              >
                <Plus className="w-3 h-3" />
              </button>
            </div>

            <button
              onClick={() => onRemove(item.id)}
              className="text-red-500 hover:text-red-700 transition-colors p-1"
              title="移除商品"
            >
              <Trash2 className="w-3 h-3" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
