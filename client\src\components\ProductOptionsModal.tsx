'use client';

import { useState, useEffect } from 'react';
import { X, Plus, Minus } from 'lucide-react';

interface ProductOption {
  id: number;
  optionType: string;
  optionName: string;
  additionalPrice: number;
}

interface Product {
  id: number;
  name: string;
  price: number;
  description?: string;
  image?: string;
  options?: ProductOption[];
}

interface SelectedOptions {
  sugar?: string;
  ice?: string;
  toppings: string[];
}

interface ProductOptionsModalProps {
  product: Product;
  isOpen: boolean;
  onClose: () => void;
  onAddToCart: (product: Product, options: SelectedOptions, totalPrice: number) => void;
}

export default function ProductOptionsModal({ 
  product, 
  isOpen, 
  onClose, 
  onAddToCart 
}: ProductOptionsModalProps) {
  const [quantity, setQuantity] = useState(1);
  const [selectedOptions, setSelectedOptions] = useState<SelectedOptions>({
    sugar: '',
    ice: '',
    toppings: []
  });

  // 設定預設值
  useEffect(() => {
    if (product.options && isOpen) {
      const sugarOptions = product.options.filter(opt => opt.optionType === 'sugar');
      const iceOptions = product.options.filter(opt => opt.optionType === 'ice');
      
      setSelectedOptions({
        sugar: sugarOptions.find(opt => opt.optionName === '正常糖')?.optionName || 
               sugarOptions[0]?.optionName || '',
        ice: iceOptions.find(opt => opt.optionName === '正常冰')?.optionName || 
             iceOptions[0]?.optionName || '',
        toppings: []
      });
      setQuantity(1);
    }
  }, [product, isOpen]);

  // 計算總價
  const calculateTotalPrice = () => {
    let additionalPrice = 0;
    
    if (product.options) {
      // 計算配件加價
      selectedOptions.toppings.forEach(toppingName => {
        const topping = product.options!.find(
          opt => opt.optionType === 'topping' && opt.optionName === toppingName
        );
        if (topping) {
          additionalPrice += topping.additionalPrice;
        }
      });
    }
    
    return (product.price + additionalPrice) * quantity;
  };

  // 處理選項變更
  const handleOptionChange = (optionType: string, optionName: string) => {
    if (optionType === 'topping') {
      setSelectedOptions(prev => ({
        ...prev,
        toppings: prev.toppings.includes(optionName)
          ? prev.toppings.filter(t => t !== optionName)
          : [...prev.toppings, optionName]
      }));
    } else {
      setSelectedOptions(prev => ({
        ...prev,
        [optionType]: optionName
      }));
    }
  };

  // 處理加入購物車
  const handleAddToCart = () => {
    onAddToCart(product, selectedOptions, calculateTotalPrice());
    onClose();
  };

  // 獲取選項組
  const getOptionsByType = (type: string) => {
    return product.options?.filter(opt => opt.optionType === type) || [];
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-3xl shadow-warm max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* 標題欄 */}
        <div className="sticky top-0 bg-white rounded-t-3xl border-b border-gray-100 p-6 flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-800">客製化選項</h2>
          <button
            onClick={onClose}
            className="p-2 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6">
          {/* 產品資訊 */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">{product.name}</h3>
            <p className="text-gray-600 text-sm mb-3">{product.description}</p>
            <p className="text-orange-500 font-bold">基本價格: NT$ {product.price}</p>
          </div>

          {/* 甜度選擇 */}
          {getOptionsByType('sugar').length > 0 && (
            <div className="mb-6">
              <h4 className="font-semibold text-gray-800 mb-3">甜度</h4>
              <div className="grid grid-cols-2 gap-2">
                {getOptionsByType('sugar').map((option) => (
                  <button
                    key={option.id}
                    onClick={() => handleOptionChange('sugar', option.optionName)}
                    className={`p-3 rounded-xl border-2 transition-colors ${
                      selectedOptions.sugar === option.optionName
                        ? 'border-orange-500 bg-orange-50 text-orange-700'
                        : 'border-gray-200 hover:border-orange-300'
                    }`}
                  >
                    {option.optionName}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* 冰量選擇 */}
          {getOptionsByType('ice').length > 0 && (
            <div className="mb-6">
              <h4 className="font-semibold text-gray-800 mb-3">冰量</h4>
              <div className="grid grid-cols-2 gap-2">
                {getOptionsByType('ice').map((option) => (
                  <button
                    key={option.id}
                    onClick={() => handleOptionChange('ice', option.optionName)}
                    className={`p-3 rounded-xl border-2 transition-colors ${
                      selectedOptions.ice === option.optionName
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-200 hover:border-blue-300'
                    }`}
                  >
                    {option.optionName}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* 配件選擇 */}
          {getOptionsByType('topping').length > 0 && (
            <div className="mb-6">
              <h4 className="font-semibold text-gray-800 mb-3">配件加料</h4>
              <div className="space-y-2">
                {getOptionsByType('topping').map((option) => (
                  <button
                    key={option.id}
                    onClick={() => handleOptionChange('topping', option.optionName)}
                    className={`w-full p-3 rounded-xl border-2 transition-colors flex items-center justify-between ${
                      selectedOptions.toppings.includes(option.optionName)
                        ? 'border-green-500 bg-green-50 text-green-700'
                        : 'border-gray-200 hover:border-green-300'
                    }`}
                  >
                    <span>{option.optionName}</span>
                    <span className="text-sm">
                      {option.additionalPrice > 0 ? `+NT$ ${option.additionalPrice}` : '免費'}
                    </span>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* 數量選擇 */}
          <div className="mb-6">
            <h4 className="font-semibold text-gray-800 mb-3">數量</h4>
            <div className="flex items-center justify-center gap-4">
              <button
                onClick={() => setQuantity(Math.max(1, quantity - 1))}
                className="p-2 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors"
              >
                <Minus className="w-5 h-5" />
              </button>
              <span className="text-xl font-semibold w-12 text-center">{quantity}</span>
              <button
                onClick={() => setQuantity(quantity + 1)}
                className="p-2 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors"
              >
                <Plus className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* 總價顯示 */}
          <div className="mb-6 p-4 bg-gray-50 rounded-xl">
            <div className="flex justify-between items-center">
              <span className="font-semibold text-gray-800">總計</span>
              <span className="text-2xl font-bold text-orange-500">
                NT$ {calculateTotalPrice()}
              </span>
            </div>
            {calculateTotalPrice() > product.price * quantity && (
              <p className="text-sm text-gray-600 mt-1">
                包含配件加價 NT$ {calculateTotalPrice() - product.price * quantity}
              </p>
            )}
          </div>

          {/* 確認按鈕 */}
          <button
            onClick={handleAddToCart}
            className="w-full py-4 px-6 gradient-orange text-white rounded-2xl font-semibold btn-hover shadow-warm"
          >
            加入購物車 - NT$ {calculateTotalPrice()}
          </button>
        </div>
      </div>
    </div>
  );
}
