'use client';

import ProtectedRoute from '@/components/ProtectedRoute';
import { useLiff } from '@/providers/LiffProvider';
import Link from 'next/link';
import { ShoppingCart, ArrowLeft, Plus, Minus, Trash2, Coffee } from 'lucide-react';
import { useState } from 'react';
import { useCart } from '@/hooks/useCart';
import apiService from '@/utils/api';
import { useRouter } from 'next/navigation';

export default function Cart() {
  const { userProfile } = useLiff();
  const router = useRouter();
  const {
    cartItems,
    updateQuantity,
    removeFromCart,
    clearCart,
    getTotalPrice,
    getTotalQuantity,
    isEmpty,
    formatOrderData
  } = useCart();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [orderType, setOrderType] = useState<'immediate' | 'scheduled'>('immediate');
  const [scheduledTime, setScheduledTime] = useState('');
  const [note, setNote] = useState('');

  // 提交訂單
  const handleSubmitOrder = async () => {
    if (isEmpty) return;

    setIsSubmitting(true);
    try {
      const orderData = formatOrderData(
        orderType,
        orderType === 'scheduled' ? new Date(scheduledTime) : undefined,
        note || undefined
      );

      const response = await apiService.orders.create(orderData);

      // 清空購物車
      clearCart();

      // 跳轉到訂單頁面
      router.push('/orders');
    } catch (error: any) {
      console.error('Failed to create order:', error);
      alert(error.response?.data?.message || '訂單建立失敗，請稍後再試');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 獲取最小預約時間（當前時間 + 30分鐘）
  const getMinScheduledTime = () => {
    const now = new Date();
    now.setMinutes(now.getMinutes() + 30);
    return now.toISOString().slice(0, 16);
  };

  return (
    <ProtectedRoute requireAuth={true} requireVerification={true}>
      <div className="min-h-screen gradient-bg">
        {/* 頂部導航欄 */}
        <header className="bg-white/80 backdrop-blur-md shadow-soft sticky top-0 z-50">
          <div className="container mx-auto px-4 py-4 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Link
                href="/products"
                className="p-2 bg-green-100 text-green-600 rounded-full hover:bg-green-200 transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </Link>
              <div className="flex items-center gap-2">
                <ShoppingCart className="w-6 h-6 text-green-500" />
                <h1 className="text-xl font-bold text-gray-800">購物車</h1>
              </div>
            </div>
            <div className="text-sm text-gray-600">
              {getTotalQuantity()} 件商品
            </div>
          </div>
        </header>

        {/* 主要內容 */}
        <main className="container mx-auto px-4 py-6">
          {isEmpty ? (
            /* 空購物車狀態 */
            <div className="text-center py-12">
              <ShoppingCart className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h2 className="text-xl font-bold text-gray-600 mb-2">購物車是空的</h2>
              <p className="text-gray-500 mb-6">快去選購您喜愛的飲品吧！</p>
              <Link
                href="/products"
                className="inline-block px-6 py-3 bg-green-500 text-white rounded-full hover:bg-green-600 transition-colors"
              >
                開始點餐
              </Link>
            </div>
          ) : (
            <div className="max-w-2xl mx-auto space-y-6">
              {/* 購物車商品列表 */}
              <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm p-6">
                <h2 className="text-xl font-bold text-gray-800 mb-4">商品清單</h2>
                <div className="space-y-4">
                  {cartItems.map((item) => (
                    <div key={item.id} className="flex items-center gap-4 p-4 bg-gray-50 rounded-xl">
                      <div className="w-16 h-16 bg-gradient-to-br from-orange-100 to-orange-200 rounded-lg flex items-center justify-center">
                        {item.image ? (
                          <img
                            src={item.image}
                            alt={item.name}
                            className="w-full h-full object-cover rounded-lg"
                          />
                        ) : (
                          <Coffee className="w-8 h-8 text-orange-400" />
                        )}
                      </div>

                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-800">{item.name}</h3>
                        <p className="text-sm text-gray-600">
                          基本價格: NT$ {item.basePrice}
                          {item.totalPrice > item.basePrice && (
                            <span className="text-orange-500 ml-2">
                              (含加料: NT$ {item.totalPrice})
                            </span>
                          )}
                        </p>
                        {item.customizations && Object.keys(item.customizations).length > 0 && (
                          <div className="text-xs text-gray-500 mt-2 space-y-1">
                            {item.customizations.sugar && (
                              <div className="flex items-center gap-1">
                                <span className="bg-orange-100 text-orange-700 px-2 py-1 rounded-full">
                                  甜度: {item.customizations.sugar}
                                </span>
                              </div>
                            )}
                            {item.customizations.ice && (
                              <div className="flex items-center gap-1">
                                <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                                  冰量: {item.customizations.ice}
                                </span>
                              </div>
                            )}
                            {item.customizations.toppings && item.customizations.toppings.length > 0 && (
                              <div className="flex items-center gap-1 flex-wrap">
                                {item.customizations.toppings.map((topping: string, index: number) => (
                                  <span key={index} className="bg-green-100 text-green-700 px-2 py-1 rounded-full">
                                    {topping}
                                  </span>
                                ))}
                              </div>
                            )}
                          </div>
                        )}
                      </div>

                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          className="p-1 bg-gray-200 rounded-full hover:bg-gray-300 transition-colors"
                        >
                          <Minus className="w-4 h-4" />
                        </button>
                        <span className="w-8 text-center font-semibold">{item.quantity}</span>
                        <button
                          onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          className="p-1 bg-gray-200 rounded-full hover:bg-gray-300 transition-colors"
                        >
                          <Plus className="w-4 h-4" />
                        </button>
                      </div>

                      <div className="text-right">
                        <p className="font-semibold text-gray-800">
                          NT$ {item.totalPrice * item.quantity}
                        </p>
                        <button
                          onClick={() => removeFromCart(item.id)}
                          className="text-red-500 hover:text-red-700 transition-colors mt-1"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 訂單選項 */}
              <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm p-6">
                <h2 className="text-xl font-bold text-gray-800 mb-4">訂單選項</h2>

                {/* 訂單類型 */}
                <div className="mb-4">
                  <label className="block text-gray-700 font-medium mb-2">訂單類型</label>
                  <div className="flex gap-4">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        value="immediate"
                        checked={orderType === 'immediate'}
                        onChange={(e) => setOrderType(e.target.value as 'immediate')}
                        className="mr-2"
                      />
                      立即訂購
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        value="scheduled"
                        checked={orderType === 'scheduled'}
                        onChange={(e) => setOrderType(e.target.value as 'scheduled')}
                        className="mr-2"
                      />
                      預約訂購
                    </label>
                  </div>
                </div>

                {/* 預約時間 */}
                {orderType === 'scheduled' && (
                  <div className="mb-4">
                    <label className="block text-gray-700 font-medium mb-2">預約時間</label>
                    <input
                      type="datetime-local"
                      value={scheduledTime}
                      onChange={(e) => setScheduledTime(e.target.value)}
                      min={getMinScheduledTime()}
                      className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                      required
                    />
                  </div>
                )}

                {/* 備註 */}
                <div className="mb-4">
                  <label className="block text-gray-700 font-medium mb-2">備註</label>
                  <textarea
                    value={note}
                    onChange={(e) => setNote(e.target.value)}
                    placeholder="有什麼特殊需求嗎？"
                    className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                    rows={3}
                  />
                </div>
              </div>

              {/* 總計和結帳 */}
              <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm p-6">
                <div className="flex justify-between items-center mb-4">
                  <span className="text-xl font-bold text-gray-800">總計</span>
                  <span className="text-2xl font-bold text-green-500">
                    NT$ {getTotalPrice()}
                  </span>
                </div>

                <button
                  onClick={handleSubmitOrder}
                  disabled={isSubmitting || (orderType === 'scheduled' && !scheduledTime)}
                  className="w-full py-4 px-6 bg-green-500 text-white rounded-2xl font-semibold hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                >
                  {isSubmitting ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      處理中...
                    </>
                  ) : (
                    '確認訂單'
                  )}
                </button>
              </div>
            </div>
          )}
        </main>
      </div>
    </ProtectedRoute>
  );
}
