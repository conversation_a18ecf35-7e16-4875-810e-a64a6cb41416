const axios = require('axios');

// 測試 lineLogin API
async function testLineLogin() {
  try {
    console.log('Testing lineLogin API...');
    
    // 模擬一個假的 access token 來測試 API 端點
    const testAccessToken = 'test_access_token_123';
    
    const response = await axios.post('http://localhost:5000/api/auth/line-login', {
      accessToken: testAccessToken
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ API endpoint is accessible');
    console.log('Response:', response.data);
    
  } catch (error) {
    if (error.response) {
      console.log('❌ API Error:', error.response.status, error.response.data);
    } else if (error.request) {
      console.log('❌ Network Error: Server not responding');
    } else {
      console.log('❌ Error:', error.message);
    }
  }
}

// 測試伺服器是否運行
async function testServerHealth() {
  try {
    const response = await axios.get('http://localhost:5000/');
    console.log('✅ Server is running');
    console.log('Response:', response.data);
  } catch (error) {
    console.log('❌ Server is not running or not accessible');
  }
}

async function runTests() {
  console.log('=== Testing Server Health ===');
  await testServerHealth();
  
  console.log('\n=== Testing LineLogin API ===');
  await testLineLogin();
}

runTests();
