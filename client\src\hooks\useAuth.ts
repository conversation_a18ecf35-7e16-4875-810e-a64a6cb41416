'use client';

import { useState, useEffect } from 'react';
import { useLiff } from '@/providers/LiffProvider';
import apiService, { tokenManager } from '@/utils/api';
import { ensureBackendLogin } from '@/utils/liff';

// 會員狀態類型
export interface UserStatus {
  isRegistered: boolean;
  isPhoneVerified: boolean;
  isLineVerified: boolean;
  isFullyVerified: boolean;
  userInfo: any | null;
  loading: boolean;
  error: string | null;
}

// 會員狀態檢查 Hook
export const useAuth = () => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const { isInitialized, isLoggedIn, userProfile } = useLiff();
  const [userStatus, setUserStatus] = useState<UserStatus>({
    isRegistered: false,
    isPhoneVerified: false,
    isLineVerified: false,
    isFullyVerified: false,
    userInfo: null,
    loading: true,
    error: null,
  });

  // 檢查會員狀態
  const checkUserStatus = async () => {
    if (!isInitialized || !isLoggedIn || !userProfile) {
      setUserStatus(prev => ({
        ...prev,
        loading: false,
        isRegistered: false,
        isPhoneVerified: false,
        isLineVerified: false,
        isFullyVerified: false,
        userInfo: null,
      }));
      return;
    }

    try {
      setUserStatus(prev => ({ ...prev, loading: true, error: null }));

      // 確保有 JWT token
      if (!tokenManager.hasValidToken()) {
        console.log('No valid JWT token, performing backend login...');
        const loginSuccess = await ensureBackendLogin();

        if (!loginSuccess) {
          throw new Error('Failed to obtain JWT token');
        }
      }

      // 調用API檢查用戶資料
      const userData = await apiService.user.getProfile();

      if (userData && userData.id) {
        const isRegistered = !!userData.id;
        const isPhoneVerified = !!userData.isVerified; // 手機驗證狀態
        const isLineVerified = !!userData.lineVerified; // LINE 驗證狀態
        const isFullyVerified = isRegistered && (isPhoneVerified || isLineVerified); // 任一驗證通過即可

        setUserStatus({
          isRegistered,
          isPhoneVerified,
          isLineVerified,
          isFullyVerified,
          userInfo: userData,
          loading: false,
          error: null,
        });
      } else {
        // 用戶未註冊
        setUserStatus({
          isRegistered: false,
          isPhoneVerified: false,
          isLineVerified: false,
          isFullyVerified: false,
          userInfo: null,
          loading: false,
          error: null,
        });
      }
    } catch (error: any) {
      console.error('檢查會員狀態失敗:', error);

      // 如果是404錯誤，表示用戶未註冊
      if (error.response?.status === 404) {
        setUserStatus({
          isRegistered: false,
          isPhoneVerified: false,
          isLineVerified: false,
          isFullyVerified: false,
          userInfo: null,
          loading: false,
          error: null,
        });
      } else {
        setUserStatus(prev => ({
          ...prev,
          loading: false,
          error: error.response?.data?.message || '檢查會員狀態失敗',
        }));
      }
    }
  };

  // 重新檢查會員狀態
  const refreshUserStatus = () => {
    checkUserStatus();
  };

  // 開發模式的模擬用戶資料
  const mockUserData = {
    id: '1',
    lineId: 'Udf723d52cde2495367205e5751fb8c8d',
    name: 'Andy瑄',
    phone:  '0912345678',
    isVerified: true,
    createdAt: '2025-05-26 08:35:21',
    updatedAt: '2025-05-26 08:35:43',
  };

  // 當LIFF初始化完成且用戶登入時檢查狀態
  useEffect(() => {
    if (isDevelopment && process.env.NEXT_PUBLIC_SKIP_LIFF_LOGIN === 'true') {
      console.log('開發模式：已繞過 LIFF 登入');
      setUserStatus(prev => ({
        ...prev,
        loading: false,
        isRegistered: true,
        isPhoneVerified: true,
        isLineVerified: true,
        isFullyVerified: true,
        userInfo: mockUserData,
      }));
      //return;
    }

    else if (isInitialized) {
      checkUserStatus();
    }
  }, [isInitialized, isLoggedIn, userProfile]);

  return {
    ...userStatus,
    refreshUserStatus,
  };
};

export default useAuth;
