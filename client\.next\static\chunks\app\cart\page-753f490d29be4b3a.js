(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5],{2322:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});var a=t(5155);function l(e){var s,t,l;let{item:r,onEditItem:i,canEdit:n=!1}=e;return(0,a.jsx)("div",{className:"border-l-4 border-purple-200 pl-4 py-3 bg-gray-50 rounded-r-lg",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[n&&i?(0,a.jsxs)("button",{onClick:()=>i(r),className:"font-medium text-purple-600 hover:text-purple-800 hover:underline transition-colors",children:[(null==(s=r.product)?void 0:s.name)||"商品"," \xd7 ",r.quantity]}):(0,a.jsxs)("span",{className:"font-medium text-gray-800",children:[(null==(t=r.product)?void 0:t.name)||"商品"," \xd7 ",r.quantity]}),n&&(0,a.jsx)("span",{className:"text-xs text-gray-400",children:"(點擊修改)"})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["原價: NT$ ",(null==(l=r.product)?void 0:l.price)||0]}),(()=>{if(!r.customizations)return null;let e=[];return r.customizations.sugar&&e.push((0,a.jsxs)("span",{className:"text-xs text-orange-600",children:["\uD83C\uDF6F ",r.customizations.sugar]},"sugar")),r.customizations.ice&&e.push((0,a.jsxs)("span",{className:"text-xs text-blue-600",children:["\uD83E\uDDCA ",r.customizations.ice]},"ice")),e.length>0?(0,a.jsx)("div",{className:"flex items-center gap-3 mt-1",children:e}):null})(),(()=>{var e;if(!(null==(e=r.customizations)?void 0:e.toppings)||0===r.customizations.toppings.length)return null;let s=e=>({珍珠:10,椰果:10,鮮奶油:15,巧克力片:10,額外奶泡:10,布丁:12,仙草:8})[e]||10;return(0,a.jsx)("div",{className:"mt-2 space-y-1",children:r.customizations.toppings.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,a.jsxs)("span",{className:"text-green-600 flex items-center gap-1",children:[(0,a.jsx)("span",{children:"\uD83E\uDD64"}),(0,a.jsx)("span",{children:e})]}),(0,a.jsxs)("span",{className:"text-orange-500 font-medium",children:["+NT$ ",s(e)]})]},t))})})()]}),(0,a.jsxs)("div",{className:"text-right ml-4",children:[(0,a.jsxs)("div",{className:"font-semibold text-gray-800",children:["NT$ ",r.price*r.quantity]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["單價: NT$ ",r.price]})]})]})})}},5078:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var a=t(5155),l=t(9053),r=t(4222),i=t(6874),n=t.n(i),c=t(7550),d=t(7809),o=t(7712),m=t(4616);let x=(0,t(9946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var u=t(2115),h=t(3841),p=t(2302),g=t(5695),b=t(2322),j=t(1352);function N(){let{userProfile:e}=(0,r.x)(),s=(0,g.useRouter)(),{cartItems:t,addToCart:i,updateQuantity:N,removeFromCart:f,clearCart:v,getTotalPrice:y,getTotalQuantity:w,isEmpty:k,formatOrderData:C}=(0,h._)(),[A,q]=(0,u.useState)(!1),[z,D]=(0,u.useState)("immediate"),[I,S]=(0,u.useState)(""),[E,_]=(0,u.useState)(""),[T,M]=(0,u.useState)(null),[$,F]=(0,u.useState)(!1),O=async()=>{if(!k){q(!0);try{let e=C(z,"scheduled"===z?new Date(I):void 0,E||void 0);await p.A.orders.create(e),v(),s.push("/orders")}catch(s){var e,t;console.error("Failed to create order:",s),alert((null==(t=s.response)||null==(e=t.data)?void 0:e.message)||"訂單建立失敗，請稍後再試")}finally{q(!1)}}},P=e=>{M({...{id:e.productId,name:e.name,price:e.basePrice,description:"",image:e.image,options:[]},cartItem:e}),F(!0)};return(0,a.jsx)(l.A,{requireAuth:!0,requireVerification:!0,children:(0,a.jsxs)("div",{className:"min-h-screen gradient-bg",children:[(0,a.jsx)("header",{className:"bg-white/80 backdrop-blur-md shadow-soft sticky top-0 z-50",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(n(),{href:"/products",className:"p-2 bg-green-100 text-green-600 rounded-full hover:bg-green-200 transition-colors",children:(0,a.jsx)(c.A,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"w-6 h-6 text-green-500"}),(0,a.jsx)("h1",{className:"text-xl font-bold text-gray-800",children:"購物車"})]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[w()," 件商品"]})]})}),(0,a.jsx)("main",{className:"container mx-auto px-4 py-6",children:k?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(d.A,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-600 mb-2",children:"購物車是空的"}),(0,a.jsx)("p",{className:"text-gray-500 mb-6",children:"快去選購您喜愛的飲品吧！"}),(0,a.jsx)(n(),{href:"/products",className:"inline-block px-6 py-3 bg-green-500 text-white rounded-full hover:bg-green-600 transition-colors",children:"開始點餐"})]}):(0,a.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-4",children:"商品清單"}),(0,a.jsx)("div",{className:"space-y-3",children:t.map(e=>{let s={id:e.id,productId:e.productId,quantity:e.quantity,price:e.totalPrice,customizations:e.customizations,product:{id:e.productId,name:e.name,price:e.basePrice,image:e.image}};return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(b.A,{item:s,onEditItem:P,canEdit:!0}),(0,a.jsxs)("div",{className:"absolute top-3 right-3 flex items-center gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1 bg-white rounded-full px-2 py-1 shadow-sm",children:[(0,a.jsx)("button",{onClick:()=>N(e.id,e.quantity-1),className:"p-1 hover:bg-gray-100 rounded-full transition-colors",children:(0,a.jsx)(o.A,{className:"w-3 h-3"})}),(0,a.jsx)("span",{className:"w-6 text-center text-sm font-medium",children:e.quantity}),(0,a.jsx)("button",{onClick:()=>N(e.id,e.quantity+1),className:"p-1 hover:bg-gray-100 rounded-full transition-colors",children:(0,a.jsx)(m.A,{className:"w-3 h-3"})})]}),(0,a.jsx)("button",{onClick:()=>f(e.id),className:"p-2 bg-red-100 text-red-600 rounded-full hover:bg-red-200 transition-colors",title:"移除商品",children:(0,a.jsx)(x,{className:"w-3 h-3"})})]})]},e.id)})})]}),(0,a.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-4",children:"訂單選項"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"訂單類型"}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"radio",value:"immediate",checked:"immediate"===z,onChange:e=>D(e.target.value),className:"mr-2"}),"立即訂購"]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"radio",value:"scheduled",checked:"scheduled"===z,onChange:e=>D(e.target.value),className:"mr-2"}),"預約訂購"]})]})]}),"scheduled"===z&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"預約時間"}),(0,a.jsx)("input",{type:"datetime-local",value:I,onChange:e=>S(e.target.value),min:(()=>{let e=new Date;return e.setMinutes(e.getMinutes()+30),e.toISOString().slice(0,16)})(),className:"w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",required:!0})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"備註"}),(0,a.jsx)("textarea",{value:E,onChange:e=>_(e.target.value),placeholder:"有什麼特殊需求嗎？",className:"w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",rows:3})]})]}),(0,a.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("span",{className:"text-xl font-bold text-gray-800",children:"總計"}),(0,a.jsxs)("span",{className:"text-2xl font-bold text-green-500",children:["NT$ ",y()]})]}),(0,a.jsx)("button",{onClick:O,disabled:A||"scheduled"===z&&!I,className:"w-full py-4 px-6 bg-green-500 text-white rounded-2xl font-semibold hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:A?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),"處理中..."]}):"確認訂單"})]})]})}),T&&(0,a.jsx)(j.A,{product:T,isOpen:$,onClose:()=>{F(!1),M(null)},onAddToCart:(e,s,t)=>{f(T.cartItem.id),i(e,s,t),F(!1),M(null)}})]})})}},7018:(e,s,t)=>{Promise.resolve().then(t.bind(t,5078))}},e=>{var s=s=>e(e.s=s);e.O(0,[932,659,222,142,441,684,358],()=>s(7018)),_N_E=e.O()}]);