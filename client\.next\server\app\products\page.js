(()=>{var e={};e.id=571,e.ids=[571],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},2412:e=>{"use strict";e.exports=require("assert")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3320:(e,t,s)=>{Promise.resolve().then(s.bind(s,8547))},3816:(e,t,s)=>{Promise.resolve().then(s.bind(s,4319))},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4319:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var r=s(687),a=s(769),i=s(584),n=s(5814),l=s.n(n),o=s(3166),c=s(8559),d=s(8561),x=s(2688);let p=(0,x.A)("flame",[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]]);var u=s(6085);let m=(0,x.A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);var h=s(6474),g=s(3210);s(7327);var j=s(3515),f=s(118);function b(){let{userProfile:e}=(0,i.x)(),{addToCart:t,getTotalQuantity:s,getTotalPrice:n}=(0,j._)(),[x,b]=(0,g.useState)("all"),[v,N]=(0,g.useState)([]),[w,y]=(0,g.useState)([]),[A,k]=(0,g.useState)(!0),[q,_]=(0,g.useState)(null),[P,C]=(0,g.useState)(null),[z,M]=(0,g.useState)(!1),D=e=>{C(e),M(!0)},S=e=>{e.options&&e.options.length>0?D(e):t(e)},G="all"===x?w:w.filter(e=>e.category===x);return A?(0,r.jsx)(a.A,{requireAuth:!0,requireVerification:!0,children:(0,r.jsx)("div",{className:"min-h-screen gradient-bg flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"載入產品中..."})]})})}):q?(0,r.jsx)(a.A,{requireAuth:!0,requireVerification:!0,children:(0,r.jsx)("div",{className:"min-h-screen gradient-bg flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(o.A,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-red-500 mb-4",children:q}),(0,r.jsx)("button",{onClick:()=>window.location.reload(),className:"px-6 py-3 bg-orange-500 text-white rounded-full hover:bg-orange-600 transition-colors",children:"重新載入"})]})})}):(0,r.jsx)(a.A,{requireAuth:!0,requireVerification:!0,children:(0,r.jsxs)("div",{className:"min-h-screen gradient-bg",children:[(0,r.jsx)("header",{className:"bg-white/80 backdrop-blur-md shadow-soft sticky top-0 z-50",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(l(),{href:"/",className:"p-2 bg-orange-100 text-orange-600 rounded-full hover:bg-orange-200 transition-colors",children:(0,r.jsx)(c.A,{className:"w-5 h-5"})}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(o.A,{className:"w-6 h-6 text-orange-500"}),(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-800",children:"點餐"})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:e?.displayName}),(0,r.jsxs)(l(),{href:"/cart",className:"relative",children:[(0,r.jsx)("div",{className:"p-2 bg-green-100 text-green-600 rounded-full hover:bg-green-200 transition-colors",children:(0,r.jsx)(d.A,{className:"w-5 h-5"})}),s()>0&&(0,r.jsx)("div",{className:"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center",children:s()})]})]})]})}),(0,r.jsxs)("main",{className:"container mx-auto px-4 py-6",children:[(0,r.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm p-6 mb-6",children:[(0,r.jsxs)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:["歡迎點餐！",e?.displayName," \uD83D\uDC4B"]}),(0,r.jsx)("p",{className:"text-gray-600",children:"選擇您喜愛的飲品，我們為您精心調製"})]}),(0,r.jsx)("div",{className:"sticky top-[73px] z-40 bg-gradient-to-b from-orange-50 to-transparent pb-4 mb-6",children:(0,r.jsx)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-soft",children:(0,r.jsx)("div",{className:"flex gap-2 overflow-x-auto pb-2",children:v.map(e=>(0,r.jsx)("button",{onClick:()=>b(e),className:`px-4 py-2 rounded-full whitespace-nowrap transition-colors shadow-sm ${x===e?"bg-orange-500 text-white shadow-orange-200":"bg-white text-gray-600 hover:bg-orange-100 border border-gray-200"}`,children:"all"===e?"全部":e},e))})})}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pb-32",children:G.map(e=>(0,r.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm overflow-hidden hover:shadow-lg transition-shadow relative",children:[(0,r.jsxs)("div",{className:"absolute top-3 left-3 z-10 flex gap-2",children:[e.isPopular&&(0,r.jsxs)("div",{className:"flex items-center gap-1 px-2 py-1 bg-red-500 text-white text-xs font-bold rounded-full shadow-lg",children:[(0,r.jsx)(p,{className:"w-3 h-3"}),"熱門"]}),e.isNew&&(0,r.jsxs)("div",{className:"flex items-center gap-1 px-2 py-1 bg-green-500 text-white text-xs font-bold rounded-full shadow-lg",children:[(0,r.jsx)(u.A,{className:"w-3 h-3"}),"新品"]})]}),(0,r.jsx)("div",{className:"h-48 bg-gradient-to-br from-orange-100 to-orange-200 flex items-center justify-center",children:e.image?(0,r.jsx)("img",{src:e.image,alt:e.name,className:"w-full h-full object-cover"}):(0,r.jsx)(o.A,{className:"w-16 h-16 text-orange-400"})}),(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("h3",{className:"font-bold text-gray-800",children:e.name}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(m,{className:"w-4 h-4 text-yellow-400 fill-current"}),(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"4.5"})]})]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:e.description||"美味飲品，值得品嚐"}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("span",{className:"text-xl font-bold text-orange-500",children:["NT$ ",e.price]}),(0,r.jsxs)("button",{onClick:()=>S(e),className:"flex items-center gap-2 px-4 py-2 bg-orange-500 text-white rounded-full hover:bg-orange-600 transition-colors",children:[(0,r.jsx)(h.A,{className:"w-4 h-4"}),e.options&&e.options.length>0?"客製化":"加入購物車"]})]})]})]},e.id))}),0===G.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(o.A,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-500",children:"此分類暫無商品"})]})]}),s()>0&&(0,r.jsx)("div",{className:"fixed bottom-0 left-0 right-0 z-50 p-4 bg-gradient-to-t from-white via-white to-transparent",children:(0,r.jsx)("div",{className:"container mx-auto max-w-md",children:(0,r.jsxs)(l(),{href:"/cart",className:"flex items-center justify-between w-full py-4 px-6 gradient-green text-white rounded-2xl shadow-warm btn-hover",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(d.A,{className:"w-6 h-6"}),(0,r.jsx)("div",{className:"absolute -top-2 -right-2 w-5 h-5 bg-white text-green-600 text-xs rounded-full flex items-center justify-center font-bold",children:s()})]}),(0,r.jsx)("span",{className:"font-semibold",children:"查看購物車"})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("p",{className:"text-sm opacity-90",children:"總計"}),(0,r.jsxs)("p",{className:"font-bold text-lg",children:["NT$ ",n()]})]})]})})}),P&&(0,r.jsx)(f.A,{product:P,isOpen:z,onClose:()=>{M(!1),C(null)},onAddToCart:(e,s,r)=>{t(e,s,r),M(!1),C(null)}})]})})}},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6085:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},7910:e=>{"use strict";e.exports=require("stream")},8354:e=>{"use strict";e.exports=require("util")},8547:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\0_project\\\\augment-projects\\\\cocodrink-line\\\\client\\\\src\\\\app\\\\products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\products\\page.tsx","default")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9506:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=s(5239),a=s(8088),i=s(8170),n=s.n(i),l=s(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c={children:["",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8547)),"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\products\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\products\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/products/page",pathname:"/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,173,248,847,507],()=>s(9506));module.exports=r})();