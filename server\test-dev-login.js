const axios = require('axios');

// 測試開發模式的 LINE 登入功能
async function testDevLogin() {
  const baseURL = 'http://localhost:5000/api';
  
  console.log('🧪 Testing Development Mode LINE Login...\n');

  // 測試用的 access tokens
  const testTokens = [
    'dev_token_1',
    'dev_token_2', 
    'dev_token_3',
    'dev_random_token'
  ];

  for (let i = 0; i < testTokens.length; i++) {
    const token = testTokens[i];
    console.log(`\n--- 測試 ${i + 1}: ${token} ---`);
    
    try {
      // 第一次登入
      console.log('第一次登入...');
      const response1 = await axios.post(`${baseURL}/auth/line-login`, {
        accessToken: token
      });
      
      console.log('✅ 登入成功');
      console.log(`用戶: ${response1.data.user.name}`);
      console.log(`Email: ${response1.data.user.email || '無'}`);
      console.log(`頭像: ${response1.data.user.pictureUrl || '無'}`);
      console.log(`是否為新用戶: ${response1.data.isNewUser}`);
      console.log(`資料是否有異動: ${response1.data.hasChanges}`);
      
      // 儲存 JWT token 以便後續測試
      const jwtToken = response1.data.token;
      
      // 第二次登入（測試資料異動檢測）
      console.log('\n第二次登入（測試資料異動檢測）...');
      const response2 = await axios.post(`${baseURL}/auth/line-login`, {
        accessToken: token
      });
      
      console.log('✅ 第二次登入成功');
      console.log(`是否為新用戶: ${response2.data.isNewUser}`);
      console.log(`資料是否有異動: ${response2.data.hasChanges}`);
      
      // 測試獲取用戶日誌
      console.log('\n測試獲取用戶日誌...');
      try {
        const logsResponse = await axios.get(`${baseURL}/auth/logs`, {
          headers: {
            'Authorization': `Bearer ${jwtToken}`
          }
        });
        
        console.log('✅ 日誌獲取成功');
        console.log(`日誌數量: ${logsResponse.data.logs.length}`);
        
        // 顯示最近的日誌
        logsResponse.data.logs.slice(0, 3).forEach((log, index) => {
          console.log(`  ${index + 1}. ${log.action} - ${new Date(log.createdAt).toLocaleString()}`);
        });
        
      } catch (logError) {
        console.log('❌ 日誌獲取失敗:', logError.response?.data?.message || logError.message);
      }
      
      // 測試登出
      console.log('\n測試登出...');
      try {
        const logoutResponse = await axios.post(`${baseURL}/auth/logout`, {}, {
          headers: {
            'Authorization': `Bearer ${jwtToken}`
          }
        });
        
        console.log('✅ 登出成功');
        console.log(`登出時間: ${logoutResponse.data.timestamp}`);
        
      } catch (logoutError) {
        console.log('❌ 登出失敗:', logoutError.response?.data?.message || logoutError.message);
      }
      
    } catch (error) {
      console.log('❌ 測試失敗');
      if (error.response) {
        console.log(`狀態碼: ${error.response.status}`);
        console.log(`錯誤訊息: ${error.response.data.message || error.response.data}`);
      } else {
        console.log(`錯誤: ${error.message}`);
      }
    }
  }
  
  console.log('\n🎉 測試完成！');
}

// 檢查伺服器是否運行
async function checkServer() {
  try {
    const response = await axios.get('http://localhost:5000/');
    console.log('✅ 伺服器正在運行');
    console.log(`回應: ${response.data.message}`);
    return true;
  } catch (error) {
    console.log('❌ 伺服器未運行或無法連接');
    console.log('請確保執行: cd server && npm run dev');
    return false;
  }
}

// 主函數
async function main() {
  console.log('🔧 開發模式 LINE 登入測試工具\n');
  
  const serverRunning = await checkServer();
  if (!serverRunning) {
    return;
  }
  
  console.log('\n開始測試...');
  await testDevLogin();
}

main().catch(console.error);
