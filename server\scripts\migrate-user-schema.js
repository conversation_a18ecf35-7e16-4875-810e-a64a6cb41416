const mysql = require('mysql2/promise');
require('dotenv').config();

async function migrateUserSchema() {
  let connection;

  try {
    // 創建資料庫連接
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'cocodrink'
    });

    console.log('Connected to database');

    // 檢查 email 欄位是否已存在
    const [emailColumns] = await connection.execute(`
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'users' AND COLUMN_NAME = 'email'
    `, [process.env.DB_NAME || 'cocodrink']);

    if (emailColumns.length === 0) {
      console.log('Adding email column to users table...');
      await connection.execute(`
        ALTER TABLE users
        ADD COLUMN email VARCHAR(255) NULL AFTER phone
      `);
      console.log('Email column added successfully');
    } else {
      console.log('Email column already exists');
    }

    // 檢查 pictureUrl 欄位是否已存在
    const [pictureColumns] = await connection.execute(`
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'users' AND COLUMN_NAME = 'pictureUrl'
    `, [process.env.DB_NAME || 'cocodrink']);

    if (pictureColumns.length === 0) {
      console.log('Adding pictureUrl column to users table...');
      await connection.execute(`
        ALTER TABLE users
        ADD COLUMN pictureUrl TEXT NULL AFTER email
      `);
      console.log('PictureUrl column added successfully');
    } else {
      console.log('PictureUrl column already exists');
    }

    // 修改 phone 欄位為可空值
    console.log('Modifying phone column to allow NULL...');
    await connection.execute(`
      ALTER TABLE users
      MODIFY COLUMN phone VARCHAR(20) NULL
    `);
    console.log('Phone column modified successfully');

    // 檢查 lineVerified 欄位是否已存在
    const [lineVerifiedColumns] = await connection.execute(`
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'users' AND COLUMN_NAME = 'lineVerified'
    `, [process.env.DB_NAME || 'cocodrink']);

    if (lineVerifiedColumns.length === 0) {
      console.log('Adding lineVerified column to users table...');
      await connection.execute(`
        ALTER TABLE users
        ADD COLUMN lineVerified BOOLEAN DEFAULT FALSE AFTER isVerified
      `);
      console.log('LineVerified column added successfully');
    } else {
      console.log('LineVerified column already exists');
    }

    // 檢查 userLogs 表是否已存在
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'userLogs'
    `, [process.env.DB_NAME || 'cocodrink']);

    if (tables.length === 0) {
      console.log('Creating userLogs table...');
      await connection.execute(`
        CREATE TABLE userLogs (
          id INT AUTO_INCREMENT PRIMARY KEY,
          userId INT NOT NULL,
          action ENUM('login', 'logout', 'profile_update') NOT NULL,
          oldData JSON NULL,
          newData JSON NULL,
          ipAddress VARCHAR(45) NULL,
          userAgent TEXT NULL,
          createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
          updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
        )
      `);

      // 創建索引
      await connection.execute(`CREATE INDEX idx_userLogs_userId ON userLogs(userId)`);
      await connection.execute(`CREATE INDEX idx_userLogs_action ON userLogs(action)`);
      await connection.execute(`CREATE INDEX idx_userLogs_createdAt ON userLogs(createdAt)`);

      console.log('UserLogs table created successfully');
    } else {
      console.log('UserLogs table already exists');
    }

    console.log('Database migration completed successfully!');

  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 執行遷移
migrateUserSchema();
