(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/utils/api.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "tokenManager": (()=>tokenManager)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
;
// API 基礎 URL
const API_BASE_URL = ("TURBOPACK compile-time value", "https://orangedrink-api2.zeabur.app/api") || 'http://localhost:5000/api';
console.log('API_BASE_URL:', API_BASE_URL);
// JWT Token 管理
const JWT_TOKEN_KEY = 'jwt_token';
const tokenManager = {
    // 儲存 JWT token
    setToken: (token)=>{
        if ("TURBOPACK compile-time truthy", 1) {
            localStorage.setItem(JWT_TOKEN_KEY, token);
        }
    },
    // 取得 JWT token
    getToken: ()=>{
        if ("TURBOPACK compile-time truthy", 1) {
            return localStorage.getItem(JWT_TOKEN_KEY);
        }
        "TURBOPACK unreachable";
    },
    // 移除 JWT token
    removeToken: ()=>{
        if ("TURBOPACK compile-time truthy", 1) {
            localStorage.removeItem(JWT_TOKEN_KEY);
        }
    },
    // 檢查是否有有效的 token
    hasValidToken: ()=>{
        const token = tokenManager.getToken();
        if (!token) return false;
        try {
            // 簡單檢查 JWT 格式
            const parts = token.split('.');
            if (parts.length !== 3) return false;
            // 檢查是否過期（可選）
            const payload = JSON.parse(atob(parts[1]));
            const now = Math.floor(Date.now() / 1000);
            return payload.exp ? payload.exp > now : true;
        } catch (error) {
            return false;
        }
    }
};
// 創建 axios 實例
const api = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json'
    }
});
// 創建不需要 JWT 驗證的 axios 實例
const publicApi = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json'
    }
});
// 請求攔截器：添加 JWT token 到請求頭
api.interceptors.request.use((config)=>{
    const token = tokenManager.getToken();
    console.log('JWT Token:', token ? `${token.substring(0, 20)}...` : 'None');
    if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// 響應攔截器：處理錯誤
api.interceptors.response.use((response)=>{
    return response;
}, (error)=>{
    // 處理 401 未授權錯誤
    if (error.response && error.response.status === 401) {
        console.error('Unauthorized, please login again');
        // 清除無效的 JWT token
        tokenManager.removeToken();
    // 可以在這裡觸發重新登入流程
    }
    return Promise.reject(error);
});
// API 服務
const apiService = {
    // 身份驗證相關 API
    auth: {
        // LINE 登入
        lineLogin: async (accessToken)=>{
            // 使用 publicApi 避免自動添加 JWT token
            const response = await publicApi.post('/auth/line-login', {
                accessToken
            });
            console.log('publicApi.post:', '/auth/line-login');
            console.log('publicApi.post accessToken:', accessToken);
            console.log('LINE login response:', response.data);
            // 儲存 JWT token
            if (response.data.token) {
                tokenManager.setToken(response.data.token);
                console.log('JWT token saved successfully');
            }
            return response.data;
        },
        // 註冊
        register: async (lineId, name, phone)=>{
            const response = await publicApi.post('/auth/register', {
                lineId,
                name,
                phone
            });
            return response.data;
        },
        // 發送手機驗證碼
        sendVerification: async (phone)=>{
            const response = await publicApi.post('/auth/send-verification', {
                phone
            });
            return response.data;
        },
        // 驗證手機驗證碼
        verifyPhone: async (phone, code)=>{
            const response = await publicApi.post('/auth/verify-phone', {
                phone,
                code
            });
            return response.data;
        },
        // 登出
        logout: async ()=>{
            const response = await api.post('/auth/logout');
            // 清除 JWT token
            tokenManager.removeToken();
            console.log('JWT token removed');
            return response.data;
        },
        // 獲取用戶日誌
        getUserLogs: async (page = 1, limit = 20, action)=>{
            const params = new URLSearchParams({
                page: page.toString(),
                limit: limit.toString()
            });
            if (action) {
                params.append('action', action);
            }
            const response = await api.get(`/auth/logs?${params}`);
            return response.data;
        }
    },
    // 用戶相關 API
    user: {
        // 獲取用戶資料
        getProfile: async ()=>{
            const response = await api.get('/user/profile');
            return response.data;
        },
        // 更新用戶資料
        updateProfile: async (data)=>{
            const response = await api.put('/user/profile', data);
            return response.data;
        },
        // 獲取用戶訂單歷史
        getOrders: async ()=>{
            const response = await api.get('/user/orders');
            return response.data;
        }
    },
    // 產品相關 API
    products: {
        // 獲取所有產品
        getAll: async ()=>{
            const response = await api.get('/products');
            return response.data;
        },
        // 獲取產品分類
        getCategories: async ()=>{
            const response = await api.get('/products/categories');
            return response.data;
        },
        // 獲取特定分類的產品
        getByCategory: async (category)=>{
            const response = await api.get(`/products/category/${category}`);
            return response.data;
        },
        // 獲取特定產品詳情
        getById: async (id)=>{
            const response = await api.get(`/products/${id}`);
            return response.data;
        }
    },
    // 訂單相關 API
    orders: {
        // 創建新訂單
        create: async (orderData)=>{
            const response = await api.post('/orders', orderData);
            return response.data;
        },
        // 獲取訂單詳情
        getById: async (id)=>{
            const response = await api.get(`/orders/${id}`);
            return response.data;
        },
        // 取消訂單
        cancel: async (id)=>{
            const response = await api.put(`/orders/${id}/cancel`);
            return response.data;
        }
    }
};
const __TURBOPACK__default__export__ = apiService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/liff-dev.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// 開發模式的 LIFF 模擬器
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "devLoginWithBackend": (()=>devLoginWithBackend),
    "initDevMode": (()=>initDevMode),
    "liffDevMock": (()=>liffDevMock)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/api.ts [app-client] (ecmascript)");
;
// 模擬的 LIFF 用戶資料
const mockUsers = [
    {
        accessToken: 'dev_token_andy',
        profile: {
            userId: 'Udf723d52cde2495367205e5751fb8c8d',
            displayName: 'Andy瑄',
            pictureUrl: 'https://profile.line-scdn.net/0hhnplyM1hN0FdFR30vaZJPi1FNCt-ZG5TIyRxIjgdaSI3IyMVI3svdGwRYCQzJ3AUI3V_LzoXa3FRBkAnQ0PLdVolanBhI3UQcHV_pw'
        }
    },
    {
        accessToken: 'dev_token_1',
        profile: {
            userId: 'U1234567890abcdef1234567890abcdef',
            displayName: '測試用戶一',
            pictureUrl: 'https://profile.line-scdn.net/0h1234567890abcdef_large'
        }
    },
    {
        accessToken: 'dev_token_2',
        profile: {
            userId: 'U2345678901bcdef12345678901bcdef1',
            displayName: '測試用戶二',
            pictureUrl: 'https://profile.line-scdn.net/0h2345678901bcdef_large'
        }
    },
    {
        accessToken: 'dev_token_3',
        profile: {
            userId: 'U3456789012cdef123456789012cdef12',
            displayName: '測試用戶三',
            pictureUrl: null
        }
    }
];
// 開發模式狀態
let devModeState = {
    isLoggedIn: false,
    currentUser: null,
    isInClient: true // 模擬在 LINE 應用內
};
const liffDevMock = {
    // 初始化
    init: async (config)=>{
        console.log('🔧 Dev Mode: LIFF initialized with config:', config);
        return Promise.resolve();
    },
    // 檢查是否已登入
    isLoggedIn: ()=>{
        return devModeState.isLoggedIn;
    },
    // 檢查是否在 LINE 應用內
    isInClient: ()=>{
        return devModeState.isInClient;
    },
    // 模擬登入
    login: (userIndex = 0)=>{
        const user = mockUsers[userIndex] || mockUsers[0];
        devModeState.isLoggedIn = true;
        devModeState.currentUser = user;
        console.log('🔧 Dev Mode: User logged in:', user.profile.displayName);
        // 儲存到 localStorage 以便重新載入後保持狀態
        localStorage.setItem('dev_liff_user', JSON.stringify(user));
        localStorage.setItem('dev_liff_logged_in', 'true');
    },
    // 模擬登出
    logout: ()=>{
        devModeState.isLoggedIn = false;
        devModeState.currentUser = null;
        console.log('🔧 Dev Mode: User logged out');
        // 清除 localStorage
        localStorage.removeItem('dev_liff_user');
        localStorage.removeItem('dev_liff_logged_in');
    },
    // 取得 access token
    getAccessToken: ()=>{
        if (!devModeState.isLoggedIn || !devModeState.currentUser) {
            return null;
        }
        return devModeState.currentUser.accessToken;
    },
    // 取得用戶資料
    getProfile: async ()=>{
        if (!devModeState.isLoggedIn || !devModeState.currentUser) {
            throw new Error('User is not logged in');
        }
        return devModeState.currentUser.profile;
    },
    // 取得 ID Token (模擬)
    getIDToken: ()=>{
        if (!devModeState.isLoggedIn) {
            return null;
        }
        // 返回一個模擬的 JWT token
        return 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2FjY2Vzcy5saW5lLm1lIiwic3ViIjoiVTEyMzQ1Njc4OTBhYmNkZWYxMjM0NTY3ODkwYWJjZGVmIiwiYXVkIjoiMTIzNDU2Nzg5MCIsImV4cCI6MTYzMjQ2NzIwMCwiaWF0IjoxNjMyNDYzNjAwLCJuYW1lIjoi5ris6Kmm55So5oi25LiAIiwicGljdHVyZSI6Imh0dHBzOi8vcHJvZmlsZS5saW5lLXNjZG4ubmV0LzBoMTIzNDU2Nzg5MGFiY2RlZl9sYXJnZSJ9.mock_signature';
    },
    // 關閉視窗
    closeWindow: ()=>{
        console.log('🔧 Dev Mode: Close window called');
    },
    // 恢復狀態（從 localStorage）
    restoreState: ()=>{
        const savedUser = localStorage.getItem('dev_liff_user');
        const isLoggedIn = localStorage.getItem('dev_liff_logged_in') === 'true';
        if (savedUser && isLoggedIn) {
            devModeState.currentUser = JSON.parse(savedUser);
            devModeState.isLoggedIn = true;
            console.log('🔧 Dev Mode: State restored for user:', devModeState.currentUser.profile.displayName);
        }
    },
    // 切換用戶（開發用）
    switchUser: (userIndex)=>{
        if (userIndex >= 0 && userIndex < mockUsers.length) {
            liffDevMock.login(userIndex);
            window.location.reload(); // 重新載入以更新狀態
        }
    },
    // 取得所有可用的測試用戶
    getAvailableUsers: ()=>{
        return mockUsers.map((user, index)=>({
                index,
                name: user.profile.displayName,
                userId: user.profile.userId
            }));
    }
};
const devLoginWithBackend = async (userIndex = 0)=>{
    try {
        // 模擬 LIFF 登入
        liffDevMock.login(userIndex);
        // 取得 access token
        const accessToken = liffDevMock.getAccessToken();
        if (!accessToken) {
            throw new Error('Unable to get access token');
        }
        // 調用後端 API 進行登入
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].auth.lineLogin(accessToken);
        console.log('🔧 Dev Mode: Backend login successful:', response);
        return response;
    } catch (error) {
        console.error('🔧 Dev Mode: Backend login failed:', error);
        throw error;
    }
};
const initDevMode = ()=>{
    // 恢復之前的狀態
    liffDevMock.restoreState();
    // 在開發工具中添加全域變數以便調試
    if ("TURBOPACK compile-time truthy", 1) {
        window.liffDev = liffDevMock;
        window.devLogin = devLoginWithBackend;
        console.log('🔧 Dev Mode initialized!');
        console.log('Available commands:');
        console.log('- window.liffDev.switchUser(0-2): 切換測試用戶');
        console.log('- window.devLogin(userIndex): 執行完整登入流程');
        console.log('- window.liffDev.getAvailableUsers(): 查看可用用戶');
    }
};
const __TURBOPACK__default__export__ = liffDevMock;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/liff.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearAuthTokens": (()=>clearAuthTokens),
    "clearUserData": (()=>clearUserData),
    "closeLiff": (()=>closeLiff),
    "default": (()=>__TURBOPACK__default__export__),
    "getAccessToken": (()=>getAccessToken),
    "getIdToken": (()=>getIdToken),
    "getUserProfile": (()=>getUserProfile),
    "initializeLiff": (()=>initializeLiff),
    "isInLiffBrowser": (()=>isInLiffBrowser),
    "isLoggedIn": (()=>isLoggedIn),
    "login": (()=>login),
    "loginWithBackend": (()=>loginWithBackend),
    "logout": (()=>logout),
    "logoutWithCleanup": (()=>logoutWithCleanup),
    "openExternalLink": (()=>openExternalLink),
    "shareMessage": (()=>shareMessage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@line/liff/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/api.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$liff$2d$dev$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/liff-dev.ts [app-client] (ecmascript)");
;
;
;
// LIFF ID
const LIFF_ID = ("TURBOPACK compile-time truthy", 1) ? ("TURBOPACK compile-time value", "2007460761-z2WnknvN") || '2007460761-vMp0L0WA' // 客戶端
 : ("TURBOPACK unreachable", undefined); // 伺服器端
// 檢查是否為開發模式
const isDevMode = ()=>{
    return ("TURBOPACK compile-time value", "development") === 'development' && "object" !== 'undefined' && window.location.hostname === 'localhost';
};
const initializeLiff = async ()=>{
    try {
        // 開發模式：使用模擬器
        if (isDevMode()) {
            console.log('🔧 Development mode: Using LIFF mock');
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$liff$2d$dev$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["liffDevMock"].init({
                liffId: LIFF_ID
            });
            return;
        }
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].init({
            liffId: LIFF_ID,
            withLoginOnExternalBrowser: true
        });
        console.log('LIFF initialization succeeded');
    } catch (error) {
        console.error('LIFF initialization failed', error);
        throw error;
    }
};
const isInLiffBrowser = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // 開發模式
    if (isDevMode()) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$liff$2d$dev$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["liffDevMock"].isInClient();
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isInClient();
};
const isLoggedIn = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // 開發模式
    if (isDevMode()) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$liff$2d$dev$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["liffDevMock"].isLoggedIn();
    }
    try {
        // 確保 LIFF 已初始化
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]) {
            console.warn('LIFF is not initialized yet');
            return false;
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isLoggedIn();
    } catch (error) {
        console.error('Error checking login status', error);
        return false;
    }
};
const getUserProfile = async ()=>{
    try {
        // 開發模式
        if (isDevMode()) {
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$liff$2d$dev$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["liffDevMock"].isLoggedIn()) {
                throw new Error('User is not logged in');
            }
            const profile = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$liff$2d$dev$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["liffDevMock"].getProfile();
            return {
                userId: profile.userId,
                displayName: profile.displayName,
                pictureUrl: profile.pictureUrl
            };
        }
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isLoggedIn()) {
            throw new Error('User is not logged in');
        }
        // 獲取用戶資料
        const profile = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getProfile();
        return {
            userId: profile.userId,
            displayName: profile.displayName,
            pictureUrl: profile.pictureUrl
        };
    } catch (error) {
        console.error('Failed to get user profile', error);
        throw error;
    }
};
const login = ()=>{
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isLoggedIn()) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].login();
    }
};
const loginWithBackend = async (userIndex = 0)=>{
    try {
        // 開發模式：使用專用的開發登入流程
        if (isDevMode()) {
            return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$liff$2d$dev$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["devLoginWithBackend"])(userIndex);
        }
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isLoggedIn()) {
            throw new Error('User is not logged in to LINE');
        }
        // 取得 access token
        const accessToken = getAccessToken();
        if (!accessToken) {
            throw new Error('Unable to get access token');
        }
        // 調用後端 API 進行登入
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].auth.lineLogin(accessToken);
        console.log('Backend login successful:', response);
        return response;
    } catch (error) {
        console.error('Backend login failed:', error);
        throw error;
    }
};
const clearUserData = ()=>{
    try {
        // 清理 localStorage 中的用戶相關資料
        const keysToRemove = [
            'userProfile',
            'userPreferences',
            'cartItems',
            'lastOrderId',
            'userSettings'
        ];
        keysToRemove.forEach((key)=>{
            localStorage.removeItem(key);
        });
        // 清理 sessionStorage
        sessionStorage.clear();
        console.log('User data cleared successfully');
    } catch (error) {
        console.error('Error clearing user data:', error);
    }
};
const clearAuthTokens = ()=>{
    try {
        // 清理可能儲存在 localStorage 中的 token
        const tokenKeys = [
            'access_token',
            'refresh_token',
            'id_token',
            'liff_token',
            'auth_token'
        ];
        tokenKeys.forEach((key)=>{
            localStorage.removeItem(key);
            sessionStorage.removeItem(key);
        });
        console.log('Auth tokens cleared successfully');
    } catch (error) {
        console.error('Error clearing auth tokens:', error);
    }
};
const logout = ()=>{
    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isLoggedIn()) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].logout();
        window.location.reload();
    }
};
const logoutWithCleanup = async ()=>{
    try {
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isLoggedIn()) {
            console.warn('User is not logged in');
            return;
        }
        // 1. 先調用伺服器端登出 API（如果有 token）
        try {
            const token = getAccessToken();
            if (token) {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].auth.logout();
                console.log('Server logout successful');
            }
        } catch (apiError) {
            console.warn('Server logout failed, continuing with client logout:', apiError);
        // 即使伺服器登出失敗，也繼續執行客戶端登出
        }
        // 2. 清理本地資料和 token
        clearUserData();
        clearAuthTokens();
        // 3. 執行 LIFF 登出（這會清除 LIFF 的 access token）
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].logout();
        // 4. 短暫延遲後重新載入頁面，確保登出完成
        setTimeout(()=>{
            window.location.href = '/';
        }, 100);
    } catch (error) {
        console.error('Logout failed:', error);
        // 即使出錯也嘗試重新載入頁面
        window.location.reload();
    }
};
const closeLiff = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].closeWindow();
};
const getIdToken = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isLoggedIn()) {
        return null;
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getIDToken() || null;
};
const getAccessToken = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // 開發模式
    if (isDevMode()) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$liff$2d$dev$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["liffDevMock"].getAccessToken();
    }
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isLoggedIn()) {
        return null;
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getAccessToken() || null;
};
const openExternalLink = (url)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].openWindow({
        url,
        external: true
    });
};
const shareMessage = async (messages)=>{
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isInClient()) {
        console.error('Share message is only available in LINE app');
        return;
    }
    try {
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].shareTargetPicker(messages);
    } catch (error) {
        console.error('Failed to share message', error);
        throw error;
    }
};
const __TURBOPACK__default__export__ = {
    initializeLiff,
    isInLiffBrowser,
    isLoggedIn,
    getUserProfile,
    login,
    loginWithBackend,
    logout,
    logoutWithCleanup,
    clearUserData,
    clearAuthTokens,
    closeLiff,
    getIdToken,
    getAccessToken,
    openExternalLink,
    shareMessage
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/providers/LiffProvider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "LiffProvider": (()=>LiffProvider),
    "default": (()=>__TURBOPACK__default__export__),
    "useLiff": (()=>useLiff)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@line/liff/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$liff$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/liff.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
;
// 創建 LIFF 上下文
const LiffContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])({
    isInitialized: false,
    isLoggedIn: false,
    isInClient: false,
    userProfile: null,
    error: null,
    isDevelopmentMode: false
});
// 開發模式的模擬用戶資料
const mockUserProfile = {
    userId: 'Udf723d52cde2495367205e5751fb8c8d',
    displayName: 'Andy瑄',
    pictureUrl: 'https://profile.line-scdn.net/0hhnplyM1hN0FdFR30vaZJPi1FNCt-ZG5TIyRxIjgdaSI3IyMVI3svdGwRYCQzJ3AUI3V_LzoXa3FRBkAnQ0PLdVolanBhI3UQcHV_pw'
};
const LiffProvider = ({ children })=>{
    _s();
    // 判斷是否為開發環境
    const isDevelopment = ("TURBOPACK compile-time value", "development") === 'development';
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(isDevelopment);
    const [isInClient, setIsInClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [userProfile, setUserProfile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(("TURBOPACK compile-time truthy", 1) ? mockUserProfile : ("TURBOPACK unreachable", undefined));
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "LiffProvider.useEffect": ()=>{
            // 如果是開發模式且想要繞過 LIFF 登入，直接返回
            if ("TURBOPACK compile-time truthy", 1) {
                console.log('開發模式：已繞過 LIFF 登入');
                setIsInitialized(true);
                return;
            }
            "TURBOPACK unreachable";
        }
    }["LiffProvider.useEffect"], [
        isDevelopment
    ]);
    // 提供 LIFF 上下文
    const value = {
        isInitialized,
        isLoggedIn: ("TURBOPACK compile-time truthy", 1) ? true : ("TURBOPACK unreachable", undefined),
        isInClient,
        userProfile,
        error,
        isDevelopmentMode: isDevelopment && ("TURBOPACK compile-time value", "true") === 'true'
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LiffContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/providers/LiffProvider.tsx",
        lineNumber: 92,
        columnNumber: 10
    }, this);
};
_s(LiffProvider, "Uzv4clKDKQCzb1/Jw99WkLJX+g4=");
_c = LiffProvider;
const useLiff = ()=>{
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(LiffContext);
};
_s1(useLiff, "gDsCjeeItUuvgOWf1v4qoK9RF6k=");
const __TURBOPACK__default__export__ = LiffProvider;
var _c;
__turbopack_context__.k.register(_c, "LiffProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_07cda999._.js.map