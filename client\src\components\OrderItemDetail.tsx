'use client';

interface OrderItem {
  id: number;
  productId: number;
  quantity: number;
  price: number;
  customizations?: {
    sugar?: string;
    ice?: string;
    toppings?: string[];
  };
  product?: {
    id: number;
    name: string;
    price: number;
    image?: string;
  };
}

interface OrderItemDetailProps {
  item: OrderItem;
  onEditItem?: (item: OrderItem) => void;
  canEdit?: boolean;
}

export default function OrderItemDetail({ item, onEditItem, canEdit = false }: OrderItemDetailProps) {
  // 計算加料費用
  const getAddOnPrice = () => {
    if (!item.product) return 0;
    return item.price - item.product.price;
  };

  // 格式化基本選項顯示（甜度、冰量）
  const renderBasicOptions = () => {
    if (!item.customizations) return null;

    const options = [];

    // 甜度（總是顯示）
    if (item.customizations.sugar) {
      options.push(
        <span key="sugar" className="text-xs text-orange-600">
          🍯 {item.customizations.sugar}
        </span>
      );
    }

    // 冰量（總是顯示）
    if (item.customizations.ice) {
      options.push(
        <span key="ice" className="text-xs text-blue-600">
          🧊 {item.customizations.ice}
        </span>
      );
    }

    return options.length > 0 ? (
      <div className="flex items-center gap-3 mt-1">
        {options}
      </div>
    ) : null;
  };

  // 格式化配料顯示（單獨一行，顯示價格）
  const renderToppings = () => {
    if (!item.customizations?.toppings || item.customizations.toppings.length === 0) {
      return null;
    }

    // 這裡需要從產品選項中獲取配料價格
    // 暫時使用固定價格，實際應該從 API 獲取
    const getToppingPrice = (toppingName: string) => {
      // 常見配料價格映射
      const toppingPrices: { [key: string]: number } = {
        '珍珠': 10,
        '椰果': 10,
        '鮮奶油': 15,
        '巧克力片': 10,
        '額外奶泡': 10,
        '布丁': 12,
        '仙草': 8
      };
      return toppingPrices[toppingName] || 10;
    };

    return (
      <div className="mt-2 space-y-1">
        {item.customizations.toppings.map((topping, index) => (
          <div key={index} className="flex items-center justify-between text-xs">
            <span className="text-green-600 flex items-center gap-1">
              <span>🥤</span>
              <span>{topping}</span>
            </span>
            <span className="text-orange-500 font-medium">
              +NT$ {getToppingPrice(topping)}
            </span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="border-l-4 border-purple-200 pl-4 py-3 bg-gray-50 rounded-r-lg">
      <div className="flex justify-between items-start">
        <div className="flex-1">
          {/* 商品名稱和數量 - 可點擊編輯 */}
          <div className="flex items-center gap-2">
            {canEdit && onEditItem ? (
              <button
                onClick={() => onEditItem(item)}
                className="font-medium text-purple-600 hover:text-purple-800 hover:underline transition-colors"
              >
                {item.product?.name || '商品'} × {item.quantity}
              </button>
            ) : (
              <span className="font-medium text-gray-800">
                {item.product?.name || '商品'} × {item.quantity}
              </span>
            )}
            {canEdit && (
              <span className="text-xs text-gray-400">(點擊修改)</span>
            )}
          </div>

          {/* 原商品價格 */}
          <div className="text-xs text-gray-500 mt-1">
            原價: NT$ {item.product?.price || 0}
          </div>

          {/* 基本選項（甜度、冰量）*/}
          {renderBasicOptions()}

          {/* 配料選項（單獨顯示，含價格）*/}
          {renderToppings()}
        </div>

        {/* 價格顯示 */}
        <div className="text-right ml-4">
          <div className="font-semibold text-gray-800">
            NT$ {item.price * item.quantity}
          </div>
          <div className="text-xs text-gray-500 mt-1">
            單價: NT$ {item.price}
          </div>
        </div>
      </div>
    </div>
  );
}
