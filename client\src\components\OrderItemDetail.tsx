'use client';

interface OrderItem {
  id: number;
  productId: number;
  quantity: number;
  price: number;
  customizations?: {
    sugar?: string;
    ice?: string;
    toppings?: string[];
  };
  product?: {
    id: number;
    name: string;
    price: number;
    image?: string;
  };
}

interface OrderItemDetailProps {
  item: OrderItem;
}

export default function OrderItemDetail({ item }: OrderItemDetailProps) {
  // 計算加料費用
  const getAddOnPrice = () => {
    if (!item.product) return 0;
    return item.price - item.product.price;
  };

  // 格式化選項顯示
  const renderCustomizations = () => {
    if (!item.customizations || Object.keys(item.customizations).length === 0) {
      return null;
    }

    const options = [];

    // 甜度（只顯示非預設值）
    if (item.customizations.sugar && item.customizations.sugar !== '正常糖') {
      options.push(
        <div key="sugar" className="flex items-center gap-1 text-xs text-orange-600">
          <span>🍯</span>
          <span>甜度: {item.customizations.sugar}</span>
        </div>
      );
    }

    // 冰量（只顯示非預設值）
    if (item.customizations.ice && item.customizations.ice !== '正常冰') {
      options.push(
        <div key="ice" className="flex items-center gap-1 text-xs text-blue-600">
          <span>🧊</span>
          <span>冰量: {item.customizations.ice}</span>
        </div>
      );
    }

    // 配料（總是顯示）
    if (item.customizations.toppings && item.customizations.toppings.length > 0) {
      options.push(
        <div key="toppings" className="flex items-center gap-1 text-xs text-green-600">
          <span>🥤</span>
          <span>配料: {item.customizations.toppings.join(', ')}</span>
        </div>
      );
    }

    return options.length > 0 ? (
      <div className="mt-2 space-y-1">
        {options}
      </div>
    ) : null;
  };

  return (
    <div className="border-l-4 border-purple-200 pl-4 py-2 bg-gray-50 rounded-r-lg">
      <div className="flex justify-between items-start">
        <div className="flex-1">
          {/* 商品名稱和數量 */}
          <div className="font-medium text-gray-800">
            {item.product?.name || '商品'} × {item.quantity}
          </div>
          
          {/* 基本價格資訊 */}
          <div className="text-xs text-gray-500 mt-1">
            基本價格: NT$ {item.product?.price || 0}
            {getAddOnPrice() > 0 && (
              <span className="text-orange-500 ml-2">
                加料費: +NT$ {getAddOnPrice()}
              </span>
            )}
          </div>

          {/* 客製化選項 */}
          {renderCustomizations()}
        </div>

        {/* 價格顯示 */}
        <div className="text-right ml-4">
          <div className="font-semibold text-gray-800">
            NT$ {item.price * item.quantity}
          </div>
          {getAddOnPrice() > 0 && (
            <div className="text-xs text-orange-500">
              單價含加料: NT$ {item.price}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
