module.exports = (sequelize, Sequelize) => {
  const Product = sequelize.define('product', {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: Sequelize.STRING,
      allowNull: false
    },
    category: {
      type: Sequelize.STRING,
      allowNull: false
    },
    price: {
      type: Sequelize.FLOAT,
      allowNull: false
    },
    description: {
      type: Sequelize.TEXT
    },
    image: {
      type: Sequelize.STRING
    },
    isAvailable: {
      type: Sequelize.BOOLEAN,
      defaultValue: true
    },
    isPopular: {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
      comment: '是否為熱門商品'
    },
    isNew: {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
      comment: '是否為新品'
    }
  }, {
    timestamps: true
  });

  return Product;
};
