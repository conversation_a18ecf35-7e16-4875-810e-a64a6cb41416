# 🔧 開發模式指南

本指南說明如何在開發環境中繞過 LINE LIFF 登入，但保持完整的登入流程測試。

## 📋 功能特色

- ✅ 繞過真實的 LINE API 通訊
- ✅ 模擬完整的用戶登入流程
- ✅ 支援多個測試用戶
- ✅ 完整的資料異動檢測
- ✅ 登入/登出日誌記錄
- ✅ JWT token 驗證
- ✅ 前後端完整整合

## 🚀 快速開始

### 1. 啟動後端伺服器

```bash
cd server
npm run dev
```

### 2. 啟動前端應用

```bash
cd client
npm run dev
```

### 3. 測試後端 API

```bash
cd server
node test-dev-login.js
```

## 🧪 測試用戶

系統預設提供三個測試用戶：

| Token | 用戶名稱 | Email | 頭像 |
|-------|----------|-------|------|
| `dev_token_1` | 測試用戶一 | <EMAIL> | ✅ |
| `dev_token_2` | 測試用戶二 | <EMAIL> | ✅ |
| `dev_token_3` | 測試用戶三 | 無 | ❌ |

## 🔧 使用方式

### 前端使用

#### 1. 基本登入

```typescript
import { loginWithBackend } from '@/utils/liff';

// 使用預設用戶（索引 0）
const result = await loginWithBackend();

// 使用特定用戶
const result = await loginWithBackend(1); // 使用測試用戶二
```

#### 2. 開發面板

在前端頁面中加入開發面板：

```tsx
import DevModePanel from '@/components/DevModePanel';

function App() {
  return (
    <div>
      {/* 你的應用內容 */}
      <DevModePanel />
    </div>
  );
}
```

#### 3. 瀏覽器控制台

開發模式會在瀏覽器控制台提供全域變數：

```javascript
// 切換用戶
window.liffDev.switchUser(1);

// 執行登入
window.devLogin(0);

// 查看可用用戶
window.liffDev.getAvailableUsers();
```

### 後端 API 測試

#### 1. 直接 API 調用

```bash
# 登入
curl -X POST http://localhost:5000/api/auth/line-login \
  -H "Content-Type: application/json" \
  -d '{"accessToken": "dev_token_1"}'

# 獲取日誌（需要 JWT token）
curl -X GET http://localhost:5000/api/auth/logs \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 2. 使用測試腳本

```bash
cd server
node test-dev-login.js
```

## 📊 測試場景

### 1. 新用戶註冊

```typescript
// 使用新的隨機 token
const result = await loginWithBackend();
console.log(result.isNewUser); // true
```

### 2. 資料異動檢測

```typescript
// 第一次登入
await loginWithBackend(0);

// 第二次登入（無異動）
const result = await loginWithBackend(0);
console.log(result.hasChanges); // false

// 切換到不同用戶（模擬資料異動）
const result2 = await loginWithBackend(1);
console.log(result2.hasChanges); // 可能為 true
```

### 3. 日誌記錄

```typescript
import apiService from '@/utils/api';

// 獲取用戶日誌
const logs = await apiService.auth.getUserLogs(1, 10);
console.log(logs.logs);

// 篩選特定動作
const loginLogs = await apiService.auth.getUserLogs(1, 10, 'login');
```

## 🔍 除錯技巧

### 1. 檢查開發模式狀態

```typescript
// 檢查是否在開發模式
console.log(process.env.NODE_ENV);
console.log(window.location.hostname);
```

### 2. 查看模擬資料

```typescript
// 查看當前用戶
console.log(window.liffDev.getAccessToken());

// 查看可用用戶
console.log(window.liffDev.getAvailableUsers());
```

### 3. 後端日誌

後端會在控制台顯示開發模式訊息：

```
🔧 Development mode: Using mock LINE user data
🔧 Development mode: Using mock LINE user info
```

## ⚙️ 設定

### 環境變數

確保 `.env` 檔案包含：

```env
NODE_ENV=development
```

### 自訂測試用戶

修改 `server/controllers/auth.controller.js` 中的 `getMockLineUserData` 函數：

```javascript
const mockUsers = {
  'your_custom_token': {
    profile: {
      userId: 'U_your_custom_id',
      displayName: '你的測試用戶',
      pictureUrl: 'https://example.com/avatar.jpg'
    },
    userInfo: {
      sub: 'U_your_custom_id',
      name: '你的測試用戶',
      picture: 'https://example.com/avatar.jpg',
      email: '<EMAIL>'
    }
  }
};
```

## 🚨 注意事項

1. **僅限開發環境**：開發模式只在 `NODE_ENV=development` 且 `localhost` 環境下啟用
2. **Token 格式**：開發模式的 access token 必須以 `dev_` 開頭
3. **資料庫**：確保資料庫連接正常，新的表格和欄位已創建
4. **生產環境**：在生產環境中，系統會自動使用真實的 LINE API

## 🎯 測試檢查清單

- [ ] 後端伺服器正常啟動
- [ ] 資料庫連接成功
- [ ] 新用戶註冊功能
- [ ] 現有用戶登入
- [ ] 資料異動檢測
- [ ] 登入日誌記錄
- [ ] 登出日誌記錄
- [ ] JWT token 驗證
- [ ] 用戶日誌查詢
- [ ] 前端開發面板
- [ ] 瀏覽器控制台工具

## 🔄 從開發模式切換到生產模式

1. 設定正確的 `NEXT_PUBLIC_LIFF_ID`
2. 確保 `NODE_ENV=production`
3. 移除或隱藏 `DevModePanel` 組件
4. 測試真實的 LINE LIFF 整合

---

**Happy Coding! 🎉**
