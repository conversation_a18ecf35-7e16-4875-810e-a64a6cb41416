import axios from 'axios';

// API 基礎 URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';
console.log('API_BASE_URL:', API_BASE_URL);

// JWT Token 管理
const JWT_TOKEN_KEY = 'jwt_token';

export const tokenManager = {
  // 儲存 JWT token
  setToken: (token: string) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(JWT_TOKEN_KEY, token);
    }
  },

  // 取得 JWT token
  getToken: (): string | null => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem(JWT_TOKEN_KEY);
    }
    return null;
  },

  // 移除 JWT token
  removeToken: () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(JWT_TOKEN_KEY);
    }
  },

  // 檢查是否有有效的 token
  hasValidToken: (): boolean => {
    const token = tokenManager.getToken();
    if (!token) return false;

    try {
      // 簡單檢查 JWT 格式
      const parts = token.split('.');
      if (parts.length !== 3) return false;

      // 檢查是否過期（可選）
      const payload = JSON.parse(atob(parts[1]));
      const now = Math.floor(Date.now() / 1000);

      return payload.exp ? payload.exp > now : true;
    } catch (error) {
      return false;
    }
  }
};

// 創建 axios 實例
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 創建不需要 JWT 驗證的 axios 實例
const publicApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 請求攔截器：添加 JWT token 到請求頭
api.interceptors.request.use(
  (config) => {
    const token = tokenManager.getToken();
    console.log('JWT Token:', token ? `${token.substring(0, 20)}...` : 'None');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 響應攔截器：處理錯誤
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // 處理 401 未授權錯誤
    if (error.response && error.response.status === 401) {
      console.error('Unauthorized, please login again');
      // 清除無效的 JWT token
      tokenManager.removeToken();
      // 可以在這裡觸發重新登入流程
    }
    return Promise.reject(error);
  }
);

// API 服務
const apiService = {
  // 身份驗證相關 API
  auth: {
    // LINE 登入
    lineLogin: async (accessToken: string) => {
      // 使用 publicApi 避免自動添加 JWT token
      const response = await publicApi.post('/auth/line-login', { accessToken });
      console.log('publicApi.post:', '/auth/line-login');
      console.log('publicApi.post accessToken:', accessToken);
      console.log('LINE login response:', response.data);

      // 儲存 JWT token
      if (response.data.token) {
        tokenManager.setToken(response.data.token);
        console.log('JWT token saved successfully');
      }

      return response.data;
    },

    // 註冊
    register: async (lineId: string, name: string, phone: string) => {
      const response = await publicApi.post('/auth/register', { lineId, name, phone });
      return response.data;
    },

    // 發送手機驗證碼
    sendVerification: async (phone: string) => {
      const response = await publicApi.post('/auth/send-verification', { phone });
      return response.data;
    },

    // 驗證手機驗證碼
    verifyPhone: async (phone: string, code: string) => {
      const response = await publicApi.post('/auth/verify-phone', { phone, code });
      return response.data;
    },

    // 登出
    logout: async () => {
      const response = await api.post('/auth/logout');

      // 清除 JWT token
      tokenManager.removeToken();
      console.log('JWT token removed');

      return response.data;
    },

    // 獲取用戶日誌
    getUserLogs: async (page = 1, limit = 20, action?: string) => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });

      if (action) {
        params.append('action', action);
      }

      const response = await api.get(`/auth/logs?${params}`);
      return response.data;
    },
  },

  // 用戶相關 API
  user: {
    // 獲取用戶資料
    getProfile: async () => {
      const response = await api.get('/user/profile');
      return response.data;
    },

    // 更新用戶資料
    updateProfile: async (data: any) => {
      const response = await api.put('/user/profile', data);
      return response.data;
    },

    // 獲取用戶訂單歷史
    getOrders: async () => {
      const response = await api.get('/user/orders');
      return response.data;
    },
  },

  // 產品相關 API
  products: {
    // 獲取所有產品
    getAll: async () => {
      const response = await api.get('/products');
      return response.data;
    },

    // 獲取產品分類
    getCategories: async () => {
      const response = await api.get('/products/categories');
      return response.data;
    },

    // 獲取特定分類的產品
    getByCategory: async (category: string) => {
      const response = await api.get(`/products/category/${category}`);
      return response.data;
    },

    // 獲取特定產品詳情
    getById: async (id: number) => {
      const response = await api.get(`/products/${id}`);
      return response.data;
    },
  },

  // 訂單相關 API
  orders: {
    // 創建新訂單
    create: async (orderData: any) => {
      const response = await api.post('/orders', orderData);
      return response.data;
    },

    // 獲取訂單詳情
    getById: async (id: number) => {
      const response = await api.get(`/orders/${id}`);
      return response.data;
    },

    // 取消訂單
    cancel: async (id: number) => {
      const response = await api.put(`/orders/${id}/cancel`);
      return response.data;
    },

    // 請求取消訂單（製作中狀態需要管理員確認）
    requestCancel: async (id: number, reason?: string) => {
      const response = await api.post(`/orders/${id}/request-cancel`, { reason });
      return response.data;
    },
  },
};

export default apiService;
