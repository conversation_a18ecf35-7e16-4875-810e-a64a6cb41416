import axios from 'axios';
import { getAccessToken } from './liff';

// API 基礎 URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';
console.log('API_BASE_URL:', API_BASE_URL);

// 創建 axios 實例
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 創建不需要 JWT 驗證的 axios 實例
const publicApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 請求攔截器：添加 token 到請求頭
api.interceptors.request.use(
  (config) => {
    const token = getAccessToken();
    console.log(token);
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 響應攔截器：處理錯誤
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // 處理 401 未授權錯誤
    if (error.response && error.response.status === 401) {
      // 可以在這裡處理登出邏輯
      console.error('Unauthorized, please login again');
    }
    return Promise.reject(error);
  }
);

// API 服務
const apiService = {
  // 身份驗證相關 API
  auth: {
    // LINE 登入
    lineLogin: async (accessToken: string) => {
      // 使用 publicApi 避免自動添加 JWT token
      const response = await publicApi.post('/auth/line-login', { accessToken });
      return response.data;
    },

    // 註冊
    register: async (lineId: string, name: string, phone: string) => {
      const response = await publicApi.post('/auth/register', { lineId, name, phone });
      return response.data;
    },

    // 發送手機驗證碼
    sendVerification: async (phone: string) => {
      const response = await publicApi.post('/auth/send-verification', { phone });
      return response.data;
    },

    // 驗證手機驗證碼
    verifyPhone: async (phone: string, code: string) => {
      const response = await publicApi.post('/auth/verify-phone', { phone, code });
      return response.data;
    },

    // 登出
    logout: async () => {
      const response = await api.post('/auth/logout');
      return response.data;
    },

    // 獲取用戶日誌
    getUserLogs: async (page = 1, limit = 20, action?: string) => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });

      if (action) {
        params.append('action', action);
      }

      const response = await api.get(`/auth/logs?${params}`);
      return response.data;
    },
  },

  // 用戶相關 API
  user: {
    // 獲取用戶資料
    getProfile: async () => {
      const response = await api.get('/user/profile');
      return response.data;
    },

    // 更新用戶資料
    updateProfile: async (data: any) => {
      const response = await api.put('/user/profile', data);
      return response.data;
    },

    // 獲取用戶訂單歷史
    getOrders: async () => {
      const response = await api.get('/user/orders');
      return response.data;
    },
  },

  // 產品相關 API
  products: {
    // 獲取所有產品
    getAll: async () => {
      const response = await api.get('/products');
      return response.data;
    },

    // 獲取產品分類
    getCategories: async () => {
      const response = await api.get('/products/categories');
      return response.data;
    },

    // 獲取特定分類的產品
    getByCategory: async (category: string) => {
      const response = await api.get(`/products/category/${category}`);
      return response.data;
    },

    // 獲取特定產品詳情
    getById: async (id: number) => {
      const response = await api.get(`/products/${id}`);
      return response.data;
    },
  },

  // 訂單相關 API
  orders: {
    // 創建新訂單
    create: async (orderData: any) => {
      const response = await api.post('/orders', orderData);
      return response.data;
    },

    // 獲取訂單詳情
    getById: async (id: number) => {
      const response = await api.get(`/orders/${id}`);
      return response.data;
    },

    // 取消訂單
    cancel: async (id: number) => {
      const response = await api.put(`/orders/${id}/cancel`);
      return response.data;
    },
  },
};

export default apiService;
