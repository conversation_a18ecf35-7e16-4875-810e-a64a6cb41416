'use client';

import ProtectedRoute from '@/components/ProtectedRoute';
import { useLiff } from '@/providers/LiffProvider';
import Link from 'next/link';
import { ShoppingBag, ArrowLeft, Clock, CheckCircle, XCircle, Coffee, AlertTriangle } from 'lucide-react';
import { useState, useEffect } from 'react';
import apiService from '@/utils/api';
import OrderItemDetail from '@/components/OrderItemDetail';

export default function Orders() {
  const { userProfile } = useLiff();
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [orders, setOrders] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [cancellingOrderId, setCancellingOrderId] = useState<number | null>(null);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [selectedOrderForCancel, setSelectedOrderForCancel] = useState<any | null>(null);
  const [cancelReason, setCancelReason] = useState('');

  // 訂單狀態配置
  const orderStatuses = [
    { id: 'all', name: '全部', color: 'gray' },
    { id: 'pending', name: '處理中', color: 'yellow' },
    { id: 'processing', name: '製作中', color: 'blue' },
    { id: 'completed', name: '已完成', color: 'green' },
    { id: 'cancelled', name: '已取消', color: 'red' },
  ];

  // 獲取訂單列表
  const fetchOrders = async () => {
    try {
      setLoading(true);
      const ordersData = await apiService.user.getOrders();
      setOrders(ordersData);
    } catch (err) {
      console.error('Failed to fetch orders:', err);
      setError('無法載入訂單列表');
    } finally {
      setLoading(false);
    }
  };

  // 初始化數據
  useEffect(() => {
    fetchOrders();
  }, []);

  // 處理取消訂單
  const handleCancelOrder = (order: any) => {
    setSelectedOrderForCancel(order);
    setShowCancelModal(true);
    setCancelReason('');
  };

  // 確認取消訂單
  const confirmCancelOrder = async () => {
    if (!selectedOrderForCancel) return;

    setCancellingOrderId(selectedOrderForCancel.id);
    try {
      if (selectedOrderForCancel.status === 'pending') {
        // 處理中狀態，直接取消
        await apiService.orders.cancel(selectedOrderForCancel.id);
        alert('訂單已成功取消');
      } else if (selectedOrderForCancel.status === 'processing') {
        // 製作中狀態，發送取消請求
        await apiService.orders.requestCancel(selectedOrderForCancel.id, cancelReason);
        alert('取消請求已發送，等待管理員確認');
      }

      // 重新載入訂單列表
      await fetchOrders();
    } catch (error: any) {
      console.error('Cancel order failed:', error);
      alert(error.response?.data?.message || '取消訂單失敗，請稍後再試');
    } finally {
      setCancellingOrderId(null);
      setShowCancelModal(false);
      setSelectedOrderForCancel(null);
      setCancelReason('');
    }
  };

  // 檢查訂單是否可以取消
  const canCancelOrder = (status: string) => {
    return status === 'pending' || status === 'processing';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case 'processing':
        return <Coffee className="w-5 h-5 text-blue-500" />;
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'cancelled':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    const statusObj = orderStatuses.find(s => s.id === status);
    return statusObj ? statusObj.name : status;
  };

  const getStatusColor = (status: string) => {
    const statusObj = orderStatuses.find(s => s.id === status);
    return statusObj ? statusObj.color : 'gray';
  };

  const filteredOrders = selectedStatus === 'all'
    ? orders
    : orders.filter(order => order.status === selectedStatus);

  // 格式化日期時間
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString('zh-TW'),
      time: date.toLocaleTimeString('zh-TW', { hour: '2-digit', minute: '2-digit' })
    };
  };

  // 載入中狀態
  if (loading) {
    return (
      <ProtectedRoute requireAuth={true} requireVerification={true}>
        <div className="min-h-screen gradient-bg flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">載入訂單中...</p>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  // 錯誤狀態
  if (error) {
    return (
      <ProtectedRoute requireAuth={true} requireVerification={true}>
        <div className="min-h-screen gradient-bg flex items-center justify-center">
          <div className="text-center">
            <ShoppingBag className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <p className="text-red-500 mb-4">{error}</p>
            <button
              onClick={fetchOrders}
              className="px-6 py-3 bg-purple-500 text-white rounded-full hover:bg-purple-600 transition-colors"
            >
              重新載入
            </button>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requireAuth={true} requireVerification={true}>
      <div className="min-h-screen gradient-bg">
        {/* 頂部導航欄 */}
        <header className="bg-white/80 backdrop-blur-md shadow-soft sticky top-0 z-50">
          <div className="container mx-auto px-4 py-4 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Link
                href="/"
                className="p-2 bg-purple-100 text-purple-600 rounded-full hover:bg-purple-200 transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </Link>
              <div className="flex items-center gap-2">
                <ShoppingBag className="w-6 h-6 text-purple-500" />
                <h1 className="text-xl font-bold text-gray-800">我的訂單</h1>
              </div>
            </div>
            <div className="text-sm text-gray-600">
              {userProfile?.displayName}
            </div>
          </div>
        </header>

        {/* 主要內容 */}
        <main className="container mx-auto px-4 py-6">
          {/* 歡迎訊息 */}
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm p-6 mb-6">
            <h2 className="text-2xl font-bold text-gray-800 mb-2">
              訂單記錄
            </h2>
            <p className="text-gray-600">
              查看您的訂單狀態和歷史記錄
            </p>
          </div>

          {/* 狀態篩選 */}
          <div className="mb-6">
            <div className="flex gap-2 overflow-x-auto pb-2">
              {orderStatuses.map((status) => (
                <button
                  key={status.id}
                  onClick={() => setSelectedStatus(status.id)}
                  className={`px-4 py-2 rounded-full whitespace-nowrap transition-colors ${
                    selectedStatus === status.id
                      ? 'bg-purple-500 text-white'
                      : 'bg-white text-gray-600 hover:bg-purple-100'
                  }`}
                >
                  {status.name}
                </button>
              ))}
            </div>
          </div>

          {/* 訂單列表 */}
          <div className="space-y-4">
            {filteredOrders.map((order) => {
              const { date, time } = formatDateTime(order.createdAt);
              return (
                <div
                  key={order.id}
                  className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm p-6"
                >
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="font-bold text-gray-800">訂單 #{order.id}</h3>
                      <p className="text-sm text-gray-600">
                        {date} {time}
                      </p>
                      {order.orderType === 'scheduled' && order.scheduledTime && (
                        <p className="text-sm text-orange-600">
                          預約時間: {formatDateTime(order.scheduledTime).date} {formatDateTime(order.scheduledTime).time}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(order.status)}
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                        getStatusColor(order.status) === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                        getStatusColor(order.status) === 'blue' ? 'bg-blue-100 text-blue-800' :
                        getStatusColor(order.status) === 'green' ? 'bg-green-100 text-green-800' :
                        getStatusColor(order.status) === 'red' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {getStatusText(order.status)}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-3 mb-4">
                    {order.items && order.items.map((item: any, index: number) => (
                      <OrderItemDetail
                        key={index}
                        item={item}
                        canEdit={false} // 訂單記錄不允許編輯
                      />
                    ))}
                  </div>

                  {order.note && (
                    <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-600">
                        <strong>備註:</strong> {order.note}
                      </p>
                    </div>
                  )}

                  <div className="border-t pt-4">
                    <div className="flex justify-between items-center mb-3">
                      <span className="font-bold text-gray-800">總計</span>
                      <span className="font-bold text-purple-500 text-lg">
                        NT$ {order.totalPrice}
                      </span>
                    </div>

                    {/* 取消訂單按鈕 */}
                    {canCancelOrder(order.status) && (
                      <button
                        onClick={() => handleCancelOrder(order)}
                        disabled={cancellingOrderId === order.id}
                        className="w-full py-2 px-4 bg-red-100 text-red-600 rounded-lg hover:bg-red-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                      >
                        {cancellingOrderId === order.id ? (
                          <>
                            <div className="w-4 h-4 border-2 border-red-600 border-t-transparent rounded-full animate-spin"></div>
                            處理中...
                          </>
                        ) : (
                          <>
                            <XCircle className="w-4 h-4" />
                            {order.status === 'pending' ? '取消訂單' : '申請取消'}
                          </>
                        )}
                      </button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {filteredOrders.length === 0 && (
            <div className="text-center py-12">
              <ShoppingBag className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">暫無訂單記錄</p>
              <Link
                href="/products"
                className="inline-block mt-4 px-6 py-3 bg-purple-500 text-white rounded-full hover:bg-purple-600 transition-colors"
              >
                開始點餐
              </Link>
            </div>
          )}
        </main>

        {/* 取消訂單確認模態框 */}
        {showCancelModal && selectedOrderForCancel && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-3xl shadow-warm max-w-md w-full p-6">
              <div className="text-center mb-6">
                <AlertTriangle className="w-16 h-16 text-orange-500 mx-auto mb-4" />
                <h2 className="text-xl font-bold text-gray-800 mb-2">
                  {selectedOrderForCancel.status === 'pending' ? '確認取消訂單' : '申請取消訂單'}
                </h2>
                <p className="text-gray-600">
                  訂單 #{selectedOrderForCancel.id}
                </p>
              </div>

              {selectedOrderForCancel.status === 'pending' ? (
                <div className="mb-6">
                  <p className="text-gray-700 text-center">
                    此訂單目前處於處理中狀態，可以直接取消。
                  </p>
                </div>
              ) : (
                <div className="mb-6">
                  <p className="text-gray-700 mb-3">
                    此訂單正在製作中，需要管理員確認才能取消。請說明取消原因：
                  </p>
                  <textarea
                    value={cancelReason}
                    onChange={(e) => setCancelReason(e.target.value)}
                    placeholder="請輸入取消原因..."
                    className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-red-500"
                    rows={3}
                    required
                  />
                </div>
              )}

              <div className="flex gap-3">
                <button
                  onClick={() => {
                    setShowCancelModal(false);
                    setSelectedOrderForCancel(null);
                    setCancelReason('');
                  }}
                  className="flex-1 py-3 px-4 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={confirmCancelOrder}
                  disabled={selectedOrderForCancel.status === 'processing' && !cancelReason.trim()}
                  className="flex-1 py-3 px-4 bg-red-500 text-white rounded-xl hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {selectedOrderForCancel.status === 'pending' ? '確認取消' : '發送申請'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </ProtectedRoute>
  );
}
