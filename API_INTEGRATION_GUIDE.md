# 🔗 前後端 API 串接完成指南

## 概述

我已經完成了點餐和訂單介面與後端 API 的完整串接，包括：

1. **產品管理系統**：產品列表、分類、詳情
2. **購物車系統**：商品管理、數量控制、本地儲存
3. **訂單系統**：訂單創建、狀態追蹤、歷史記錄
4. **用戶系統**：用戶資料、訂單歷史

## 🛠️ 已完成的功能

### 1. 產品頁面 (`/products`)

**功能**：
- ✅ 從 API 獲取產品列表
- ✅ 從 API 獲取產品分類
- ✅ 分類篩選功能
- ✅ 加入購物車功能
- ✅ 購物車數量顯示
- ✅ 載入和錯誤狀態處理

**API 串接**：
```typescript
// 獲取所有產品
await apiService.products.getAll()

// 獲取產品分類
await apiService.products.getCategories()

// 獲取特定分類產品
await apiService.products.getByCategory(category)
```

### 2. 購物車頁面 (`/cart`)

**功能**：
- ✅ 購物車商品管理
- ✅ 數量增減控制
- ✅ 商品移除功能
- ✅ 訂單類型選擇（立即/預約）
- ✅ 預約時間設定
- ✅ 訂單備註功能
- ✅ 總價計算
- ✅ 訂單提交

**API 串接**：
```typescript
// 創建訂單
await apiService.orders.create(orderData)
```

### 3. 訂單頁面 (`/orders`)

**功能**：
- ✅ 從 API 獲取訂單列表
- ✅ 訂單狀態篩選
- ✅ 訂單詳情顯示
- ✅ 預約時間顯示
- ✅ 訂單備註顯示
- ✅ 載入和錯誤狀態處理

**API 串接**：
```typescript
// 獲取用戶訂單歷史
await apiService.user.getOrders()
```

### 4. 購物車狀態管理

**功能**：
- ✅ 本地儲存購物車資料
- ✅ 商品數量管理
- ✅ 客製化選項支援
- ✅ 總價和總數量計算
- ✅ 訂單資料格式化

## 📁 新增的檔案

### 前端檔案：

1. **`client/src/hooks/useCart.ts`**
   - 購物車狀態管理 Hook
   - 本地儲存整合
   - 訂單資料格式化

2. **`client/src/app/cart/page.tsx`**
   - 購物車頁面
   - 訂單選項設定
   - 結帳功能

### 後端檔案：

1. **`server/scripts/seed-products.js`**
   - 測試產品資料腳本
   - 包含 8 個範例產品
   - 產品選項設定

## 🔄 修改的檔案

### 前端修改：

1. **`client/src/app/products/page.tsx`**
   - 從靜態資料改為 API 串接
   - 新增購物車功能
   - 新增載入和錯誤狀態

2. **`client/src/app/orders/page.tsx`**
   - 從靜態資料改為 API 串接
   - 新增真實訂單資料顯示
   - 新增載入和錯誤狀態

3. **`client/src/utils/api.ts`**
   - 新增用戶相關 API 方法
   - 修復重複屬性問題

## 🧪 測試步驟

### 1. 準備測試資料

```bash
# 進入 server 目錄
cd server

# 執行資料庫遷移（如果需要）
node scripts/migrate-user-schema.js

# 新增測試產品資料
node scripts/seed-products.js
```

### 2. 啟動服務

```bash
# 啟動後端服務
cd server
npm run dev

# 啟動前端服務
cd client
npm run dev
```

### 3. 測試流程

1. **登入系統**
   - 訪問 `http://localhost:3000`
   - 完成 LINE 登入或使用開發模式

2. **測試產品頁面**
   - 點擊「開始點餐」
   - 查看產品列表是否正確載入
   - 測試分類篩選功能
   - 測試加入購物車功能

3. **測試購物車**
   - 點擊購物車圖標
   - 測試數量增減功能
   - 測試訂單類型選擇
   - 測試訂單提交

4. **測試訂單頁面**
   - 點擊「我的訂單」
   - 查看訂單列表
   - 測試狀態篩選功能

## 🔧 API 端點對應

### 產品相關：
- `GET /api/products` - 獲取所有產品
- `GET /api/products/categories` - 獲取產品分類
- `GET /api/products/category/:category` - 獲取特定分類產品
- `GET /api/products/:id` - 獲取產品詳情

### 訂單相關：
- `POST /api/orders` - 創建新訂單
- `GET /api/orders/:id` - 獲取訂單詳情
- `PUT /api/orders/:id/cancel` - 取消訂單

### 用戶相關：
- `GET /api/user/profile` - 獲取用戶資料
- `PUT /api/user/profile` - 更新用戶資料
- `GET /api/user/orders` - 獲取用戶訂單歷史

## 🎯 資料流程

### 點餐流程：
1. 用戶瀏覽產品列表 → `GET /api/products`
2. 用戶加入商品到購物車 → 本地儲存
3. 用戶進入購物車頁面 → 顯示本地資料
4. 用戶提交訂單 → `POST /api/orders`
5. 清空購物車 → 跳轉到訂單頁面

### 訂單查看流程：
1. 用戶進入訂單頁面 → `GET /api/user/orders`
2. 顯示訂單列表 → 包含狀態、商品、總價
3. 用戶可篩選不同狀態的訂單

## 🔍 除錯指南

### 常見問題：

1. **產品列表載入失敗**
   - 檢查後端是否正常運行
   - 檢查是否有測試產品資料
   - 檢查 JWT token 是否有效

2. **訂單創建失敗**
   - 檢查用戶是否已驗證
   - 檢查購物車資料格式
   - 檢查預約時間格式

3. **訂單列表為空**
   - 檢查是否有建立過訂單
   - 檢查 JWT token 是否正確
   - 檢查用戶 ID 是否匹配

### 除錯工具：

1. **瀏覽器開發者工具**
   - Network 標籤查看 API 請求
   - Console 標籤查看錯誤訊息
   - Application 標籤查看 localStorage

2. **後端日誌**
   - 查看 server 控制台輸出
   - 檢查資料庫連接狀態
   - 檢查 API 響應內容

## ✅ 完成狀態

- ✅ 產品列表 API 串接
- ✅ 產品分類 API 串接
- ✅ 購物車功能實現
- ✅ 訂單創建 API 串接
- ✅ 訂單列表 API 串接
- ✅ 用戶資料 API 串接
- ✅ 錯誤處理和載入狀態
- ✅ 響應式設計
- ✅ 測試資料準備

現在您的點餐和訂單系統已經完全串接後端 API，可以進行完整的功能測試了！🎉
