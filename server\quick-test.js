const axios = require('axios');

// 快速測試 Andy瑄 的開發模式登入
async function quickTest() {
  const baseURL = 'http://localhost:5000/api';
  
  console.log('🚀 快速測試 Andy瑄 開發模式登入\n');

  try {
    // 測試 Andy瑄 的登入
    console.log('📱 使用 Andy瑄 的模擬資料登入...');
    const loginResponse = await axios.post(`${baseURL}/auth/line-login`, {
      accessToken: 'dev_token_andy'
    });
    
    console.log('✅ 登入成功！');
    console.log(`👤 用戶: ${loginResponse.data.user.name}`);
    console.log(`📧 Email: ${loginResponse.data.user.email}`);
    console.log(`🆔 Line ID: ${loginResponse.data.user.lineId}`);
    console.log(`🆕 是否為新用戶: ${loginResponse.data.isNewUser}`);
    console.log(`🔄 資料是否有異動: ${loginResponse.data.hasChanges}`);
    
    // 儲存 JWT token
    const jwtToken = loginResponse.data.token;
    console.log(`🔑 JWT Token: ${jwtToken.substring(0, 50)}...`);
    
    // 測試獲取用戶日誌
    console.log('\n📋 獲取用戶日誌...');
    const logsResponse = await axios.get(`${baseURL}/auth/logs`, {
      headers: {
        'Authorization': `Bearer ${jwtToken}`
      }
    });
    
    console.log('✅ 日誌獲取成功！');
    console.log(`📊 日誌數量: ${logsResponse.data.logs.length}`);
    
    // 顯示最近的日誌
    if (logsResponse.data.logs.length > 0) {
      console.log('\n📝 最近的日誌:');
      logsResponse.data.logs.slice(0, 3).forEach((log, index) => {
        console.log(`  ${index + 1}. ${log.action} - ${new Date(log.createdAt).toLocaleString()}`);
      });
    }
    
    // 測試登出
    console.log('\n🚪 測試登出...');
    const logoutResponse = await axios.post(`${baseURL}/auth/logout`, {}, {
      headers: {
        'Authorization': `Bearer ${jwtToken}`
      }
    });
    
    console.log('✅ 登出成功！');
    console.log(`⏰ 登出時間: ${logoutResponse.data.timestamp}`);
    
    console.log('\n🎉 所有測試通過！開發模式運作正常。');
    console.log('\n💡 提示:');
    console.log('- 前端可以使用 loginWithBackend(0) 來登入 Andy瑄');
    console.log('- 使用 DevModePanel 組件來視覺化測試');
    console.log('- 在瀏覽器控制台使用 window.devLogin(0)');
    
  } catch (error) {
    console.log('❌ 測試失敗');
    if (error.response) {
      console.log(`狀態碼: ${error.response.status}`);
      console.log(`錯誤訊息: ${error.response.data.message || error.response.data}`);
    } else if (error.request) {
      console.log('❌ 無法連接到伺服器');
      console.log('請確保執行: cd server && npm run dev');
    } else {
      console.log(`錯誤: ${error.message}`);
    }
  }
}

// 檢查伺服器狀態
async function checkServer() {
  try {
    const response = await axios.get('http://localhost:5000/');
    console.log('✅ 伺服器正在運行');
    return true;
  } catch (error) {
    console.log('❌ 伺服器未運行');
    console.log('請先啟動伺服器: cd server && npm run dev');
    return false;
  }
}

// 主函數
async function main() {
  console.log('🔧 Andy瑄 開發模式快速測試工具\n');
  
  const serverRunning = await checkServer();
  if (!serverRunning) {
    return;
  }
  
  await quickTest();
}

main().catch(console.error);
