const db = require('../models');
const User = db.users;
const UserLog = db.userLogs;
const jwt = require('jsonwebtoken');
const twilio = require('twilio');
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');

// 創建 Twilio 客戶端（僅在生產環境中）
let twilioClient = null;
if (process.env.NODE_ENV === 'production' && process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
  twilioClient = twilio(
    process.env.TWILIO_ACCOUNT_SID,
    process.env.TWILIO_AUTH_TOKEN
  );
}

// 生成 JWT 令牌
const generateToken = (user) => {
  return jwt.sign(
    { id: user.id, lineId: user.lineId },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN }
  );
};

// 生成隨機驗證碼
const generateVerificationCode = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// 開發模式：模擬 LINE 用戶資料
const getMockLineUserData = (accessToken) => {
  // 根據 access token 生成不同的模擬用戶
  const mockUsers = {
    'dev_token_1': {
      profile: {
        userId: 'U1234567890abcdef1234567890abcdef',
        displayName: '測試用戶一',
        pictureUrl: 'https://profile.line-scdn.net/0h1234567890abcdef_large'
      },
      userInfo: {
        sub: 'U1234567890abcdef1234567890abcdef',
        name: '測試用戶一',
        picture: 'https://profile.line-scdn.net/0h1234567890abcdef_large',
        email: '<EMAIL>'
      }
    },
    'dev_token_2': {
      profile: {
        userId: 'U2345678901bcdef12345678901bcdef1',
        displayName: '測試用戶二',
        pictureUrl: 'https://profile.line-scdn.net/0h2345678901bcdef_large'
      },
      userInfo: {
        sub: 'U2345678901bcdef12345678901bcdef1',
        name: '測試用戶二',
        picture: 'https://profile.line-scdn.net/0h2345678901bcdef_large',
        email: '<EMAIL>'
      }
    },
    'dev_token_3': {
      profile: {
        userId: 'U3456789012cdef123456789012cdef12',
        displayName: '測試用戶三',
        pictureUrl: null // 測試沒有頭像的情況
      },
      userInfo: {
        sub: 'U3456789012cdef123456789012cdef12',
        name: '測試用戶三',
        picture: null,
        email: null // 測試沒有 email 的情況
      }
    }
  };

  // 如果是預定義的測試 token，返回對應的模擬資料
  if (mockUsers[accessToken]) {
    return mockUsers[accessToken];
  }

  // 否則生成隨機的模擬資料
  const randomId = Math.random().toString(36).substring(2, 15);
  const userId = `U${randomId}${'0'.repeat(32 - randomId.length)}`;

  return {
    profile: {
      userId: userId,
      displayName: `開發用戶_${randomId.substring(0, 6)}`,
      pictureUrl: `https://profile.line-scdn.net/0h${randomId}_large`
    },
    userInfo: {
      sub: userId,
      name: `開發用戶_${randomId.substring(0, 6)}`,
      picture: `https://profile.line-scdn.net/0h${randomId}_large`,
      email: `dev_${randomId}@example.com`
    }
  };
};

// 從 LINE API 取得用戶資料
const getLineUserProfile = async (accessToken) => {
  // 開發模式：使用模擬資料
  if (process.env.NODE_ENV === 'development' && accessToken.startsWith('dev_')) {
    console.log('🔧 Development mode: Using mock LINE user data');
    const mockData = getMockLineUserData(accessToken);
    return mockData.profile;
  }

  try {
    const response = await axios.get('https://api.line.me/v2/profile', {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching LINE user profile:', error);
    throw new Error('無法取得 LINE 用戶資料');
  }
};

// 從 LINE API 取得用戶資訊（包含 email）
const getLineUserInfo = async (accessToken) => {
  // 開發模式：使用模擬資料
  if (process.env.NODE_ENV === 'development' && accessToken.startsWith('dev_')) {
    console.log('🔧 Development mode: Using mock LINE user info');
    const mockData = getMockLineUserData(accessToken);
    return mockData.userInfo;
  }

  try {
    const response = await axios.get('https://api.line.me/oauth2/v2.1/userinfo', {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching LINE user info:', error);
    // 如果無法取得 userinfo，返回 null（可能沒有 email scope）
    return null;
  }
};

// 記錄用戶日誌
const logUserAction = async (userId, action, oldData = null, newData = null, req = null) => {
  try {
    const logData = {
      userId,
      action,
      oldData,
      newData
    };

    if (req) {
      logData.ipAddress = req.ip || req.connection.remoteAddress;
      logData.userAgent = req.get('User-Agent');
    }

    await UserLog.create(logData);
  } catch (error) {
    console.error('Error logging user action:', error);
    // 不要因為日誌記錄失敗而中斷主要流程
  }
};

// 比較用戶資料是否有異動
const compareUserData = (oldUser, newUserData) => {
  const changes = {};
  const fieldsToCompare = ['name', 'email', 'pictureUrl'];

  fieldsToCompare.forEach(field => {
    if (oldUser[field] !== newUserData[field]) {
      changes[field] = {
        old: oldUser[field],
        new: newUserData[field]
      };
    }
  });

  return Object.keys(changes).length > 0 ? changes : null;
};

// 註冊新用戶
exports.register = async (req, res) => {
  try {
    const { lineId, name, phone } = req.body;

    // 檢查用戶是否已存在
    const existingUser = await User.findOne({
      where: {
        [db.Sequelize.Op.or]: [
          { lineId: lineId },
          { phone: phone }
        ]
      }
    });

    if (existingUser) {
      return res.status(400).json({ message: '用戶已存在' });
    }

    // 創建新用戶
    const user = await User.create({
      lineId,
      name,
      phone,
      isVerified: false
    });

    // 生成令牌
    const token = generateToken(user);

    res.status(201).json({
      message: '用戶註冊成功',
      user: {
        id: user.id,
        lineId: user.lineId,
        name: user.name,
        phone: user.phone,
        isVerified: user.isVerified
      },
      token
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: '伺服器錯誤' });
  }
};

// 發送手機驗證碼
exports.sendVerification = async (req, res) => {
  try {
    const { phone } = req.body;

    // 檢查用戶是否存在
    const user = await User.findOne({ where: { phone } });

    if (!user) {
      return res.status(404).json({ message: '用戶不存在' });
    }

    // 生成驗證碼
    const verificationCode = generateVerificationCode();

    // 設置驗證碼過期時間（10分鐘）
    const verificationExpires = new Date();
    verificationExpires.setMinutes(verificationExpires.getMinutes() + 10);

    // 更新用戶的驗證碼
    await user.update({
      verificationCode,
      verificationExpires
    });

    // 發送驗證碼簡訊
    if (process.env.NODE_ENV === 'production' && twilioClient) {
      try {
        await twilioClient.messages.create({
          body: `您的 COCO飲料店 驗證碼是：${verificationCode}，有效期為10分鐘。`,
          from: process.env.TWILIO_PHONE_NUMBER,
          to: phone
        });
      } catch (err) {
        console.error('發送簡訊失敗:', err);
        // 即使簡訊發送失敗，我們仍然繼續流程，因為這是開發環境
      }
    }

    // 無論是否在生產環境，都在控制台輸出驗證碼（方便開發和測試）
    console.log(`驗證碼：${verificationCode}`);

    res.status(200).json({ message: '驗證碼已發送' });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: '伺服器錯誤' });
  }
};

// 驗證手機驗證碼
exports.verifyPhone = async (req, res) => {
  try {
    const { phone, code } = req.body;

    // 檢查用戶是否存在
    const user = await User.findOne({ where: { phone } });

    if (!user) {
      return res.status(404).json({ message: '用戶不存在' });
    }

    // 檢查驗證碼是否正確
    if (user.verificationCode !== code) {
      return res.status(400).json({ message: '驗證碼不正確' });
    }

    // 檢查驗證碼是否過期
    if (new Date() > new Date(user.verificationExpires)) {
      return res.status(400).json({ message: '驗證碼已過期' });
    }

    // 更新用戶為已驗證
    await user.update({
      isVerified: true,
      verificationCode: null,
      verificationExpires: null
    });

    // 生成令牌
    const token = generateToken(user);

    res.status(200).json({
      message: '手機驗證成功',
      user: {
        id: user.id,
        lineId: user.lineId,
        name: user.name,
        phone: user.phone,
        isVerified: user.isVerified
      },
      token
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: '伺服器錯誤' });
  }
};

// LINE 登入
exports.lineLogin = async (req, res) => {
  try {
    const { accessToken } = req.body;

    if (!accessToken) {
      return res.status(400).json({ message: '缺少 access token' });
    }

    // 從 LINE API 取得用戶資料
    const [lineProfile, lineUserInfo] = await Promise.all([
      getLineUserProfile(accessToken),
      getLineUserInfo(accessToken)
    ]);

    const lineId = lineProfile.userId;
    const userData = {
      name: lineProfile.displayName,
      pictureUrl: lineProfile.pictureUrl || null,
      email: lineUserInfo?.email || null
    };

    // 檢查用戶是否存在
    let user = await User.findOne({ where: { lineId } });
    let isNewUser = false;
    let hasChanges = false;
    let changes = null;

    if (!user) {
      // 如果用戶不存在，創建一個新用戶
      user = await User.create({
        lineId,
        name: userData.name,
        email: userData.email,
        pictureUrl: userData.pictureUrl,
        phone: null,
        isVerified: false
      });
      isNewUser = true;

      // 記錄新用戶註冊日誌
      await logUserAction(user.id, 'login', null, {
        action: 'new_user_registration',
        userData: userData
      }, req);
    } else {
      // 檢查用戶資料是否有異動
      changes = compareUserData(user, userData);

      if (changes) {
        hasChanges = true;
        const oldData = {
          name: user.name,
          email: user.email,
          pictureUrl: user.pictureUrl
        };

        // 更新用戶資料
        await user.update(userData);

        // 記錄資料異動日誌
        await logUserAction(user.id, 'profile_update', oldData, userData, req);
      }

      // 記錄登入日誌
      await logUserAction(user.id, 'login', null, {
        hasChanges,
        changes: changes || 'no_changes'
      }, req);
    }

    // 生成令牌
    const token = generateToken(user);

    res.status(200).json({
      message: 'LINE 登入成功',
      user: {
        id: user.id,
        lineId: user.lineId,
        name: user.name,
        email: user.email,
        pictureUrl: user.pictureUrl,
        phone: user.phone,
        isVerified: user.isVerified
      },
      token,
      isNewUser,
      hasChanges,
      changes
    });
  } catch (error) {
    console.error('LINE login error:', error);
    res.status(500).json({
      message: '伺服器錯誤',
      error: error.message
    });
  }
};

// 登出
exports.logout = async (req, res) => {
  try {
    const userId = req.userId;

    // 記錄登出日誌
    await logUserAction(userId, 'logout', null, {
      timestamp: new Date().toISOString()
    }, req);

    console.log(`User ${userId} logged out at ${new Date().toISOString()}`);

    res.status(200).json({
      message: '登出成功',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({ message: '登出時發生錯誤' });
  }
};

// 獲取用戶日誌
exports.getUserLogs = async (req, res) => {
  try {
    const userId = req.userId;
    const { page = 1, limit = 20, action } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = { userId };

    if (action) {
      whereClause.action = action;
    }

    const logs = await UserLog.findAndCountAll({
      where: whereClause,
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
      attributes: ['id', 'action', 'oldData', 'newData', 'ipAddress', 'createdAt']
    });

    res.status(200).json({
      message: '用戶日誌獲取成功',
      logs: logs.rows,
      pagination: {
        total: logs.count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(logs.count / limit)
      }
    });
  } catch (error) {
    console.error('Get user logs error:', error);
    res.status(500).json({ message: '獲取用戶日誌時發生錯誤' });
  }
};