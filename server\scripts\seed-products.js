const db = require('../models');
const Product = db.products;
const ProductOption = db.productOptions;

// 測試產品數據
const sampleProducts = [
  {
    name: '珍珠奶茶',
    category: '茶類',
    price: 65,
    description: '經典珍珠奶茶，香濃茶香配上Q彈珍珠',
    image: null,
    isAvailable: true,
    isPopular: true,  // 熱門商品
    isNew: false,
    options: [
      { optionType: 'sugar', optionName: '正常糖', additionalPrice: 0 },
      { optionType: 'sugar', optionName: '半糖', additionalPrice: 0 },
      { optionType: 'sugar', optionName: '微糖', additionalPrice: 0 },
      { optionType: 'sugar', optionName: '無糖', additionalPrice: 0 },
      { optionType: 'ice', optionName: '正常冰', additionalPrice: 0 },
      { optionType: 'ice', optionName: '少冰', additionalPrice: 0 },
      { optionType: 'ice', optionName: '微冰', additionalPrice: 0 },
      { optionType: 'ice', optionName: '去冰', additionalPrice: 0 },
      { optionType: 'topping', optionName: '珍珠', additionalPrice: 10 },
      { optionType: 'topping', optionName: '椰果', additionalPrice: 10 },
    ]
  },
  {
    name: '美式咖啡',
    category: '咖啡',
    price: 55,
    description: '純正美式咖啡，濃郁香醇',
    image: null,
    isAvailable: true,
    isPopular: false,
    isNew: false,
    options: [
      { optionType: 'sugar', optionName: '正常糖', additionalPrice: 0 },
      { optionType: 'sugar', optionName: '半糖', additionalPrice: 0 },
      { optionType: 'sugar', optionName: '無糖', additionalPrice: 0 },
      { optionType: 'ice', optionName: '熱飲', additionalPrice: 0 },
      { optionType: 'ice', optionName: '正常冰', additionalPrice: 0 },
      { optionType: 'ice', optionName: '少冰', additionalPrice: 0 },
    ]
  },
  {
    name: '芒果汁',
    category: '果汁',
    price: 70,
    description: '新鮮芒果現打，酸甜可口',
    image: null,
    isAvailable: true,
    isPopular: true,  // 熱門商品
    isNew: false,
    options: [
      { optionType: 'sugar', optionName: '正常糖', additionalPrice: 0 },
      { optionType: 'sugar', optionName: '半糖', additionalPrice: 0 },
      { optionType: 'sugar', optionName: '微糖', additionalPrice: 0 },
      { optionType: 'ice', optionName: '正常冰', additionalPrice: 0 },
      { optionType: 'ice', optionName: '少冰', additionalPrice: 0 },
      { optionType: 'topping', optionName: '椰果', additionalPrice: 10 },
    ]
  },
  {
    name: '草莓奶昔',
    category: '奶昔',
    price: 80,
    description: '濃郁草莓奶昔，香甜順滑',
    image: null,
    isAvailable: true,
    isPopular: false,
    isNew: true,  // 新品
    options: [
      { optionType: 'sugar', optionName: '正常糖', additionalPrice: 0 },
      { optionType: 'sugar', optionName: '半糖', additionalPrice: 0 },
      { optionType: 'ice', optionName: '正常冰', additionalPrice: 0 },
      { optionType: 'ice', optionName: '少冰', additionalPrice: 0 },
      { optionType: 'topping', optionName: '鮮奶油', additionalPrice: 15 },
    ]
  },
  {
    name: '綠茶',
    category: '茶類',
    price: 45,
    description: '清香綠茶，回甘無窮',
    image: null,
    isAvailable: true,
    isPopular: false,
    isNew: false,
    options: [
      { optionType: 'sugar', optionName: '正常糖', additionalPrice: 0 },
      { optionType: 'sugar', optionName: '半糖', additionalPrice: 0 },
      { optionType: 'sugar', optionName: '微糖', additionalPrice: 0 },
      { optionType: 'sugar', optionName: '無糖', additionalPrice: 0 },
      { optionType: 'ice', optionName: '正常冰', additionalPrice: 0 },
      { optionType: 'ice', optionName: '少冰', additionalPrice: 0 },
      { optionType: 'ice', optionName: '微冰', additionalPrice: 0 },
      { optionType: 'ice', optionName: '去冰', additionalPrice: 0 },
    ]
  },
  {
    name: '拿鐵咖啡',
    category: '咖啡',
    price: 75,
    description: '香濃拿鐵，咖啡與牛奶的完美結合',
    image: null,
    isAvailable: true,
    isPopular: true,  // 熱門商品
    isNew: false,
    options: [
      { optionType: 'sugar', optionName: '正常糖', additionalPrice: 0 },
      { optionType: 'sugar', optionName: '半糖', additionalPrice: 0 },
      { optionType: 'sugar', optionName: '無糖', additionalPrice: 0 },
      { optionType: 'ice', optionName: '熱飲', additionalPrice: 0 },
      { optionType: 'ice', optionName: '正常冰', additionalPrice: 0 },
      { optionType: 'ice', optionName: '少冰', additionalPrice: 0 },
      { optionType: 'topping', optionName: '額外奶泡', additionalPrice: 10 },
    ]
  },
  {
    name: '柳橙汁',
    category: '果汁',
    price: 60,
    description: '新鮮柳橙現榨，維他命C滿滿',
    image: null,
    isAvailable: true,
    isPopular: false,
    isNew: false,
    options: [
      { optionType: 'sugar', optionName: '正常糖', additionalPrice: 0 },
      { optionType: 'sugar', optionName: '無糖', additionalPrice: 0 },
      { optionType: 'ice', optionName: '正常冰', additionalPrice: 0 },
      { optionType: 'ice', optionName: '少冰', additionalPrice: 0 },
    ]
  },
  {
    name: '巧克力奶昔',
    category: '奶昔',
    price: 85,
    description: '濃郁巧克力奶昔，甜蜜滿分',
    image: null,
    isAvailable: true,
    isPopular: false,
    isNew: true,  // 新品
    options: [
      { optionType: 'sugar', optionName: '正常糖', additionalPrice: 0 },
      { optionType: 'sugar', optionName: '半糖', additionalPrice: 0 },
      { optionType: 'ice', optionName: '正常冰', additionalPrice: 0 },
      { optionType: 'ice', optionName: '少冰', additionalPrice: 0 },
      { optionType: 'topping', optionName: '鮮奶油', additionalPrice: 15 },
      { optionType: 'topping', optionName: '巧克力片', additionalPrice: 10 },
    ]
  }
];

async function seedProducts() {
  try {
    console.log('開始新增測試產品數據...');

    // 清除現有產品數據
    await ProductOption.destroy({ where: {} });
    await Product.destroy({ where: {} });

    console.log('已清除現有產品數據');

    // 新增產品和選項
    for (const productData of sampleProducts) {
      const { options, ...productInfo } = productData;

      // 創建產品
      const product = await Product.create(productInfo);
      console.log(`已創建產品: ${product.name}`);

      // 創建產品選項
      if (options && options.length > 0) {
        for (const option of options) {
          await ProductOption.create({
            productId: product.id,
            ...option
          });
        }
        console.log(`已為 ${product.name} 創建 ${options.length} 個選項`);
      }
    }

    console.log('✅ 測試產品數據新增完成！');
    console.log(`總共新增了 ${sampleProducts.length} 個產品`);

  } catch (error) {
    console.error('❌ 新增測試產品數據失敗:', error);
  }
}

// 如果直接執行此腳本
if (require.main === module) {
  seedProducts().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error(error);
    process.exit(1);
  });
}

module.exports = { seedProducts };
