// 開發模式的 LIFF 模擬器
import apiService from './api';

// 模擬的 LIFF 用戶資料
const mockUsers = [
  {
    accessToken: 'dev_token_1',
    profile: {
      userId: 'U1234567890abcdef1234567890abcdef',
      displayName: '測試用戶一',
      pictureUrl: 'https://profile.line-scdn.net/0h1234567890abcdef_large'
    }
  },
  {
    accessToken: 'dev_token_2',
    profile: {
      userId: 'U2345678901bcdef12345678901bcdef1',
      displayName: '測試用戶二',
      pictureUrl: 'https://profile.line-scdn.net/0h2345678901bcdef_large'
    }
  },
  {
    accessToken: 'dev_token_3',
    profile: {
      userId: 'U3456789012cdef123456789012cdef12',
      displayName: '測試用戶三',
      pictureUrl: null
    }
  }
];

// 開發模式狀態
let devModeState = {
  isLoggedIn: false,
  currentUser: null as any,
  isInClient: true // 模擬在 LINE 應用內
};

// 開發模式的 LIFF 模擬器
export const liffDevMock = {
  // 初始化
  init: async (config: any) => {
    console.log('🔧 Dev Mode: LIFF initialized with config:', config);
    return Promise.resolve();
  },

  // 檢查是否已登入
  isLoggedIn: () => {
    return devModeState.isLoggedIn;
  },

  // 檢查是否在 LINE 應用內
  isInClient: () => {
    return devModeState.isInClient;
  },

  // 模擬登入
  login: (userIndex = 0) => {
    const user = mockUsers[userIndex] || mockUsers[0];
    devModeState.isLoggedIn = true;
    devModeState.currentUser = user;
    console.log('🔧 Dev Mode: User logged in:', user.profile.displayName);
    
    // 儲存到 localStorage 以便重新載入後保持狀態
    localStorage.setItem('dev_liff_user', JSON.stringify(user));
    localStorage.setItem('dev_liff_logged_in', 'true');
  },

  // 模擬登出
  logout: () => {
    devModeState.isLoggedIn = false;
    devModeState.currentUser = null;
    console.log('🔧 Dev Mode: User logged out');
    
    // 清除 localStorage
    localStorage.removeItem('dev_liff_user');
    localStorage.removeItem('dev_liff_logged_in');
  },

  // 取得 access token
  getAccessToken: () => {
    if (!devModeState.isLoggedIn || !devModeState.currentUser) {
      return null;
    }
    return devModeState.currentUser.accessToken;
  },

  // 取得用戶資料
  getProfile: async () => {
    if (!devModeState.isLoggedIn || !devModeState.currentUser) {
      throw new Error('User is not logged in');
    }
    return devModeState.currentUser.profile;
  },

  // 取得 ID Token (模擬)
  getIDToken: () => {
    if (!devModeState.isLoggedIn) {
      return null;
    }
    // 返回一個模擬的 JWT token
    return 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2FjY2Vzcy5saW5lLm1lIiwic3ViIjoiVTEyMzQ1Njc4OTBhYmNkZWYxMjM0NTY3ODkwYWJjZGVmIiwiYXVkIjoiMTIzNDU2Nzg5MCIsImV4cCI6MTYzMjQ2NzIwMCwiaWF0IjoxNjMyNDYzNjAwLCJuYW1lIjoi5ris6Kmm55So5oi25LiAIiwicGljdHVyZSI6Imh0dHBzOi8vcHJvZmlsZS5saW5lLXNjZG4ubmV0LzBoMTIzNDU2Nzg5MGFiY2RlZl9sYXJnZSJ9.mock_signature';
  },

  // 關閉視窗
  closeWindow: () => {
    console.log('🔧 Dev Mode: Close window called');
  },

  // 恢復狀態（從 localStorage）
  restoreState: () => {
    const savedUser = localStorage.getItem('dev_liff_user');
    const isLoggedIn = localStorage.getItem('dev_liff_logged_in') === 'true';
    
    if (savedUser && isLoggedIn) {
      devModeState.currentUser = JSON.parse(savedUser);
      devModeState.isLoggedIn = true;
      console.log('🔧 Dev Mode: State restored for user:', devModeState.currentUser.profile.displayName);
    }
  },

  // 切換用戶（開發用）
  switchUser: (userIndex: number) => {
    if (userIndex >= 0 && userIndex < mockUsers.length) {
      liffDevMock.login(userIndex);
      window.location.reload(); // 重新載入以更新狀態
    }
  },

  // 取得所有可用的測試用戶
  getAvailableUsers: () => {
    return mockUsers.map((user, index) => ({
      index,
      name: user.profile.displayName,
      userId: user.profile.userId
    }));
  }
};

// 開發模式的完整登入流程
export const devLoginWithBackend = async (userIndex = 0) => {
  try {
    // 模擬 LIFF 登入
    liffDevMock.login(userIndex);
    
    // 取得 access token
    const accessToken = liffDevMock.getAccessToken();
    if (!accessToken) {
      throw new Error('Unable to get access token');
    }

    // 調用後端 API 進行登入
    const response = await apiService.auth.lineLogin(accessToken);
    
    console.log('🔧 Dev Mode: Backend login successful:', response);
    return response;
  } catch (error) {
    console.error('🔧 Dev Mode: Backend login failed:', error);
    throw error;
  }
};

// 初始化開發模式
export const initDevMode = () => {
  // 恢復之前的狀態
  liffDevMock.restoreState();
  
  // 在開發工具中添加全域變數以便調試
  if (typeof window !== 'undefined') {
    (window as any).liffDev = liffDevMock;
    (window as any).devLogin = devLoginWithBackend;
    
    console.log('🔧 Dev Mode initialized!');
    console.log('Available commands:');
    console.log('- window.liffDev.switchUser(0-2): 切換測試用戶');
    console.log('- window.devLogin(userIndex): 執行完整登入流程');
    console.log('- window.liffDev.getAvailableUsers(): 查看可用用戶');
  }
};

export default liffDevMock;
