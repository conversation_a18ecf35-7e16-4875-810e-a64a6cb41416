import React, { useState, useEffect } from 'react';
import { liffDevMock, devLoginWithBackend } from '@/utils/liff-dev';
import { isLoggedIn, getUserProfile, loginWithBackend } from '@/utils/liff';
import apiService from '@/utils/api';

const DevModePanel: React.FC = () => {
  const [isLoggedInState, setIsLoggedInState] = useState(false);
  const [userProfile, setUserProfile] = useState<any>(null);
  const [logs, setLogs] = useState<any[]>([]);
  const [selectedUser, setSelectedUser] = useState(0);

  const availableUsers = liffDevMock.getAvailableUsers();

  useEffect(() => {
    // 初始化開發模式
    liffDevMock.restoreState();
    updateState();
  }, []);

  const updateState = async () => {
    const loggedIn = isLoggedIn();
    setIsLoggedInState(loggedIn);
    
    if (loggedIn) {
      try {
        const profile = await getUserProfile();
        setUserProfile(profile);
      } catch (error) {
        console.error('Failed to get user profile:', error);
      }
    } else {
      setUserProfile(null);
    }
  };

  const handleLogin = async () => {
    try {
      const result = await loginWithBackend(selectedUser);
      console.log('Login result:', result);
      await updateState();
      alert(`登入成功！\n用戶：${result.user.name}\n是否為新用戶：${result.isNewUser}\n資料是否有異動：${result.hasChanges}`);
    } catch (error) {
      console.error('Login failed:', error);
      alert(`登入失敗：${(error as Error).message}`);
    }
  };

  const handleLogout = () => {
    liffDevMock.logout();
    updateState();
  };

  const handleSwitchUser = (userIndex: number) => {
    setSelectedUser(userIndex);
    liffDevMock.switchUser(userIndex);
    updateState();
  };

  const fetchUserLogs = async () => {
    try {
      const response = await apiService.auth.getUserLogs(1, 10);
      setLogs(response.logs);
    } catch (error) {
      console.error('Failed to fetch logs:', error);
      alert('獲取日誌失敗，請確保已登入');
    }
  };

  // 只在開發環境顯示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-gray-800 text-white p-4 rounded-lg shadow-lg max-w-md z-50">
      <h3 className="text-lg font-bold mb-3">🔧 開發模式面板</h3>
      
      {/* 登入狀態 */}
      <div className="mb-3">
        <p className="text-sm">
          狀態: <span className={isLoggedInState ? 'text-green-400' : 'text-red-400'}>
            {isLoggedInState ? '已登入' : '未登入'}
          </span>
        </p>
        {userProfile && (
          <p className="text-xs text-gray-300">
            用戶: {userProfile.displayName}
          </p>
        )}
      </div>

      {/* 用戶選擇 */}
      <div className="mb-3">
        <label className="block text-xs mb-1">選擇測試用戶:</label>
        <select 
          value={selectedUser} 
          onChange={(e) => setSelectedUser(parseInt(e.target.value))}
          className="w-full p-1 text-black text-xs rounded"
        >
          {availableUsers.map((user, index) => (
            <option key={index} value={index}>
              {user.name} (索引: {index})
            </option>
          ))}
        </select>
      </div>

      {/* 操作按鈕 */}
      <div className="space-y-2">
        <button
          onClick={handleLogin}
          className="w-full bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-xs"
        >
          執行完整登入流程
        </button>
        
        <button
          onClick={handleLogout}
          className="w-full bg-red-600 hover:bg-red-700 px-3 py-1 rounded text-xs"
        >
          登出
        </button>

        <button
          onClick={() => handleSwitchUser(selectedUser)}
          className="w-full bg-yellow-600 hover:bg-yellow-700 px-3 py-1 rounded text-xs"
        >
          切換用戶 (重新載入頁面)
        </button>

        <button
          onClick={fetchUserLogs}
          className="w-full bg-green-600 hover:bg-green-700 px-3 py-1 rounded text-xs"
        >
          查看用戶日誌
        </button>
      </div>

      {/* 日誌顯示 */}
      {logs.length > 0 && (
        <div className="mt-3 max-h-32 overflow-y-auto">
          <h4 className="text-xs font-bold mb-1">最近日誌:</h4>
          {logs.map((log, index) => (
            <div key={index} className="text-xs bg-gray-700 p-1 mb-1 rounded">
              <span className="text-yellow-400">{log.action}</span>
              <span className="text-gray-400 ml-2">
                {new Date(log.createdAt).toLocaleString()}
              </span>
            </div>
          ))}
        </div>
      )}

      {/* 說明 */}
      <div className="mt-3 text-xs text-gray-400">
        <p>💡 提示:</p>
        <ul className="list-disc list-inside text-xs">
          <li>使用 dev_token_1/2/3 進行測試</li>
          <li>可在瀏覽器控制台使用 window.liffDev</li>
          <li>測試不同用戶的資料異動情況</li>
        </ul>
      </div>
    </div>
  );
};

export default DevModePanel;
