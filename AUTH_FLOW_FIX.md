# 🔐 認證流程問題修復

## 問題分析

從您提供的錯誤日誌可以看出：

```
JWT Token: None
GET https://orangedrink-api2.zeabur.app/api/user/profile 403 (Forbidden)
檢查會員狀態失敗: $ {message: 'Request failed with status code 403'}
```

**問題根因**：
1. LIFF 已成功初始化並取得用戶資料
2. 但沒有調用後端 `lineLogin` API 來取得 JWT token
3. 前端直接嘗試調用需要認證的 API，導致 403 錯誤

## 🔧 修復方案

### 1. 新增自動後端登入機制

在 `client/src/utils/liff.ts` 中新增：

```typescript
// 檢查並自動執行後端登入（如果需要）
export const ensureBackendLogin = async () => {
  try {
    // 檢查是否有有效的 JWT token
    if (tokenManager.hasValidToken()) {
      return true;
    }

    // 檢查 LIFF 是否已登入
    if (!isLoggedIn()) {
      return false;
    }

    // 執行後端登入
    await loginWithBackend();
    return true;
  } catch (error) {
    console.error('Auto backend login failed:', error);
    return false;
  }
};
```

### 2. 修改 useAuth Hook

在 `client/src/hooks/useAuth.ts` 中修改：

```typescript
// 確保有 JWT token
if (!tokenManager.hasValidToken()) {
  console.log('No valid JWT token, performing backend login...');
  const loginSuccess = await ensureBackendLogin();
  
  if (!loginSuccess) {
    throw new Error('Failed to obtain JWT token');
  }
}

// 調用API檢查用戶資料
const userData = await apiService.user.getProfile();
```

### 3. 修正 API 響應處理

原本的程式碼期望 API 返回 `{ success: true, data: ... }`，但實際上我們的 API 直接返回用戶資料。

## 🚀 完整認證流程

### 開發模式流程：

1. **LIFF 初始化** → 使用模擬資料
2. **檢查 JWT Token** → 如果沒有，執行步驟 3
3. **自動後端登入** → 使用 `dev_token_andy` 調用 `/auth/line-login`
4. **儲存 JWT Token** → 存到 localStorage
5. **API 調用** → 使用 JWT token 認證

### 生產模式流程：

1. **LIFF 初始化** → 真實的 LINE LIFF
2. **用戶登入** → 取得真實的 LIFF access token
3. **檢查 JWT Token** → 如果沒有，執行步驟 4
4. **自動後端登入** → 使用真實 access token 調用 `/auth/line-login`
5. **儲存 JWT Token** → 存到 localStorage
6. **API 調用** → 使用 JWT token 認證

## 🧪 測試方式

### 1. 使用測試頁面

開啟 `client/test-auth-flow.html` 來測試完整流程：

- **完整認證流程測試**：模擬從 LIFF 登入到 API 調用的完整流程
- **分步測試**：單獨測試每個步驟
- **Token 管理**：檢查和管理 JWT token
- **錯誤模擬**：測試各種錯誤情況

### 2. 前端整合測試

```typescript
import { ensureBackendLogin } from '@/utils/liff';
import apiService from '@/utils/api';

// 確保認證
const success = await ensureBackendLogin();
if (success) {
  // 現在可以安全地調用 API
  const profile = await apiService.user.getProfile();
  console.log('用戶資料:', profile);
}
```

### 3. 檢查認證狀態

```javascript
// 在瀏覽器控制台
console.log('JWT Token:', localStorage.getItem('jwt_token'));
console.log('Token Valid:', tokenManager.hasValidToken());
```

## 🔍 除錯指南

### 常見問題：

1. **403 Forbidden**
   - 檢查是否有 JWT token：`localStorage.getItem('jwt_token')`
   - 確認 token 格式正確：應該是三段式 JWT
   - 檢查 token 是否過期

2. **401 Unauthorized**
   - JWT token 無效或格式錯誤
   - 後端 JWT_SECRET 設定問題

3. **LIFF 初始化錯誤**
   - 檢查 LIFF_ID 設定
   - 確認開發模式設定正確

### 除錯步驟：

1. **檢查 LIFF 狀態**
   ```javascript
   console.log('LIFF Logged In:', liff.isLoggedIn());
   console.log('User Profile:', await liff.getProfile());
   ```

2. **檢查 JWT Token**
   ```javascript
   const token = localStorage.getItem('jwt_token');
   if (token) {
     const payload = JSON.parse(atob(token.split('.')[1]));
     console.log('Token Payload:', payload);
   }
   ```

3. **測試 API 調用**
   ```javascript
   // 手動測試
   fetch('https://orangedrink-api2.zeabur.app/api/user/profile', {
     headers: {
       'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`
     }
   }).then(r => r.json()).then(console.log);
   ```

## ✅ 驗證清單

- [ ] LIFF 成功初始化
- [ ] 自動後端登入成功
- [ ] JWT token 正確儲存
- [ ] `/api/user/profile` 返回 200
- [ ] 用戶資料包含完整信息
- [ ] 登出時清除 token
- [ ] 錯誤處理正確

## 🎯 預期結果

修復後，您應該看到：

```
✅ LIFF initialization succeeded
✅ No valid JWT token, performing backend login...
✅ Backend login successful: Andy瑄
✅ JWT token saved successfully
✅ 用戶資料獲取成功
```

而不是：
```
❌ JWT Token: None
❌ GET /api/user/profile 403 (Forbidden)
```

現在您的認證流程應該可以正常工作了！🎉
