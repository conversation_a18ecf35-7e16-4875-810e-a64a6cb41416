<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>認證流程測試</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 900px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px; white-space: pre-wrap; font-family: monospace; font-size: 12px; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .status { display: inline-block; padding: 2px 8px; border-radius: 3px; font-size: 11px; font-weight: bold; }
        .status.online { background: #d4edda; color: #155724; }
        .status.offline { background: #f8d7da; color: #721c24; }
        .flow-step { margin: 10px 0; padding: 10px; border-left: 4px solid #007bff; background: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 認證流程測試工具</h1>
        
        <div class="section">
            <h3>系統狀態</h3>
            <div id="systemStatus">
                <p>API 伺服器: <span id="apiStatus" class="status offline">檢查中...</span></p>
                <p>JWT Token: <span id="jwtStatus" class="status offline">無</span></p>
                <p>LIFF 狀態: <span id="liffStatus" class="status offline">未初始化</span></p>
            </div>
        </div>

        <div class="section">
            <h3>1. 完整認證流程測試</h3>
            <div class="flow-step">
                <strong>步驟 1:</strong> 模擬 LIFF 登入 → 取得用戶資料
            </div>
            <div class="flow-step">
                <strong>步驟 2:</strong> 調用後端登入 API → 取得 JWT token
            </div>
            <div class="flow-step">
                <strong>步驟 3:</strong> 使用 JWT token 調用用戶 API → 驗證認證
            </div>
            
            <button onclick="testFullAuthFlow()">執行完整認證流程</button>
            <button onclick="testWithAndy()">使用 Andy瑄 測試</button>
            <div id="authFlowResult" class="result"></div>
        </div>

        <div class="section">
            <h3>2. 分步測試</h3>
            <button onclick="testBackendLogin()">步驟 1: 後端登入</button>
            <button onclick="testGetProfile()">步驟 2: 獲取用戶資料</button>
            <button onclick="testLogout()">步驟 3: 登出</button>
            <div id="stepResult" class="result"></div>
        </div>

        <div class="section">
            <h3>3. Token 管理</h3>
            <button onclick="checkTokenStatus()">檢查 Token 狀態</button>
            <button onclick="clearAllTokens()">清除所有 Token</button>
            <button onclick="showTokenDetails()">顯示 Token 詳情</button>
            <div id="tokenResult" class="result"></div>
        </div>

        <div class="section">
            <h3>4. 錯誤模擬</h3>
            <button onclick="testInvalidToken()">測試無效 Token</button>
            <button onclick="testExpiredToken()">測試過期 Token</button>
            <div id="errorResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'https://orangedrink-api2.zeabur.app/api';

        // Token 管理
        const tokenManager = {
            setToken: (token) => localStorage.setItem('jwt_token', token),
            getToken: () => localStorage.getItem('jwt_token'),
            removeToken: () => localStorage.removeItem('jwt_token'),
            hasValidToken: () => {
                const token = tokenManager.getToken();
                if (!token) return false;
                try {
                    const parts = token.split('.');
                    if (parts.length !== 3) return false;
                    const payload = JSON.parse(atob(parts[1]));
                    const now = Math.floor(Date.now() / 1000);
                    return payload.exp ? payload.exp > now : true;
                } catch (error) {
                    return false;
                }
            }
        };

        // API 調用
        async function apiCall(endpoint, method = 'GET', data = null, useAuth = true) {
            const headers = { 'Content-Type': 'application/json' };
            
            if (useAuth) {
                const token = tokenManager.getToken();
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }
            }

            const config = { method, headers };
            if (data) config.body = JSON.stringify(data);

            const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
            const result = await response.json();
            
            return { status: response.status, data: result };
        }

        // 更新狀態顯示
        function updateStatus() {
            // JWT Token 狀態
            const hasJwt = tokenManager.hasValidToken();
            const jwtElement = document.getElementById('jwtStatus');
            jwtElement.textContent = hasJwt ? '有效' : '無效/不存在';
            jwtElement.className = `status ${hasJwt ? 'online' : 'offline'}`;

            // API 狀態
            checkApiStatus();
        }

        async function checkApiStatus() {
            try {
                const response = await fetch(`${API_BASE_URL.replace('/api', '')}/`);
                const apiElement = document.getElementById('apiStatus');
                if (response.ok) {
                    apiElement.textContent = '在線';
                    apiElement.className = 'status online';
                } else {
                    apiElement.textContent = '離線';
                    apiElement.className = 'status offline';
                }
            } catch (error) {
                const apiElement = document.getElementById('apiStatus');
                apiElement.textContent = '離線';
                apiElement.className = 'status offline';
            }
        }

        // 測試函數
        async function testFullAuthFlow() {
            const resultDiv = document.getElementById('authFlowResult');
            resultDiv.className = 'result';
            resultDiv.textContent = '開始完整認證流程測試...\n';

            try {
                // 步驟 1: 後端登入
                resultDiv.textContent += '\n步驟 1: 執行後端登入...\n';
                const loginResponse = await apiCall('/auth/line-login', 'POST', {
                    accessToken: 'dev_token_andy'
                }, false);

                if (loginResponse.status !== 200) {
                    throw new Error(`登入失敗: ${loginResponse.data.message}`);
                }

                tokenManager.setToken(loginResponse.data.token);
                resultDiv.textContent += `✅ 登入成功: ${loginResponse.data.user.name}\n`;

                // 步驟 2: 獲取用戶資料
                resultDiv.textContent += '\n步驟 2: 獲取用戶資料...\n';
                const profileResponse = await apiCall('/user/profile');

                if (profileResponse.status !== 200) {
                    throw new Error(`獲取用戶資料失敗: ${profileResponse.data.message}`);
                }

                resultDiv.textContent += `✅ 用戶資料獲取成功:\n`;
                resultDiv.textContent += `   ID: ${profileResponse.data.id}\n`;
                resultDiv.textContent += `   姓名: ${profileResponse.data.name}\n`;
                resultDiv.textContent += `   Email: ${profileResponse.data.email || '無'}\n`;
                resultDiv.textContent += `   手機: ${profileResponse.data.phone || '無'}\n`;
                resultDiv.textContent += `   已驗證: ${profileResponse.data.isVerified}\n`;

                resultDiv.className = 'result success';
                resultDiv.textContent += '\n🎉 完整認證流程測試成功！';

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent += `\n❌ 測試失敗: ${error.message}`;
            }

            updateStatus();
        }

        async function testWithAndy() {
            await testFullAuthFlow();
        }

        async function testBackendLogin() {
            const resultDiv = document.getElementById('stepResult');
            try {
                resultDiv.className = 'result';
                resultDiv.textContent = '執行後端登入...';

                const response = await apiCall('/auth/line-login', 'POST', {
                    accessToken: 'dev_token_andy'
                }, false);

                if (response.status === 200) {
                    tokenManager.setToken(response.data.token);
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 後端登入成功！\n用戶: ${response.data.user.name}\nJWT Token 已儲存`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 登入失敗: ${response.data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 錯誤: ${error.message}`;
            }
            updateStatus();
        }

        async function testGetProfile() {
            const resultDiv = document.getElementById('stepResult');
            try {
                resultDiv.className = 'result';
                resultDiv.textContent = '獲取用戶資料...';

                const response = await apiCall('/user/profile');

                if (response.status === 200) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 用戶資料獲取成功！\n${JSON.stringify(response.data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 獲取失敗 (${response.status}): ${response.data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 錯誤: ${error.message}`;
            }
        }

        async function testLogout() {
            const resultDiv = document.getElementById('stepResult');
            try {
                resultDiv.className = 'result';
                resultDiv.textContent = '執行登出...';

                const response = await apiCall('/auth/logout', 'POST');

                if (response.status === 200) {
                    tokenManager.removeToken();
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 登出成功！\nJWT Token 已清除`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 登出失敗: ${response.data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 錯誤: ${error.message}`;
            }
            updateStatus();
        }

        function checkTokenStatus() {
            const resultDiv = document.getElementById('tokenResult');
            const token = tokenManager.getToken();
            const isValid = tokenManager.hasValidToken();

            if (token) {
                resultDiv.className = 'result success';
                resultDiv.textContent = `Token 存在: ${token.length} 字符\n狀態: ${isValid ? '有效' : '無效或已過期'}`;
            } else {
                resultDiv.className = 'result warning';
                resultDiv.textContent = '沒有 JWT Token';
            }
        }

        function clearAllTokens() {
            tokenManager.removeToken();
            localStorage.clear();
            sessionStorage.clear();
            
            const resultDiv = document.getElementById('tokenResult');
            resultDiv.className = 'result success';
            resultDiv.textContent = '✅ 所有 Token 和本地資料已清除';
            updateStatus();
        }

        function showTokenDetails() {
            const resultDiv = document.getElementById('tokenResult');
            const token = tokenManager.getToken();

            if (token) {
                try {
                    const parts = token.split('.');
                    const header = JSON.parse(atob(parts[0]));
                    const payload = JSON.parse(atob(parts[1]));
                    
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `Token 詳情:\n\nHeader:\n${JSON.stringify(header, null, 2)}\n\nPayload:\n${JSON.stringify(payload, null, 2)}`;
                } catch (error) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Token 解析失敗: ${error.message}`;
                }
            } else {
                resultDiv.className = 'result warning';
                resultDiv.textContent = '沒有 JWT Token';
            }
        }

        async function testInvalidToken() {
            const resultDiv = document.getElementById('errorResult');
            
            // 儲存當前 token
            const originalToken = tokenManager.getToken();
            
            // 設定無效 token
            tokenManager.setToken('invalid.token.here');
            
            try {
                resultDiv.className = 'result';
                resultDiv.textContent = '測試無效 Token...';

                const response = await apiCall('/user/profile');
                
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 應該失敗但成功了: ${response.status}`;
            } catch (error) {
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 正確處理無效 Token: ${error.message}`;
            }
            
            // 恢復原始 token
            if (originalToken) {
                tokenManager.setToken(originalToken);
            } else {
                tokenManager.removeToken();
            }
        }

        // 頁面載入時初始化
        window.onload = function() {
            updateStatus();
            
            // 模擬 LIFF 狀態
            const liffElement = document.getElementById('liffStatus');
            liffElement.textContent = '已初始化 (模擬)';
            liffElement.className = 'status online';
        };
    </script>
</body>
</html>
