-- 更新用戶表，新增 email 和 pictureUrl 欄位
USE cocodrink;

-- 新增 email 欄位
ALTER TABLE users
ADD COLUMN email VARCHAR(255) NULL AFTER phone;

-- 新增 pictureUrl 欄位
ALTER TABLE users
ADD COLUMN pictureUrl TEXT NULL AFTER email;

-- 修改 phone 欄位為可空值
ALTER TABLE users
MODIFY COLUMN phone VARCHAR(20) NULL;

-- 新增 lineVerified 欄位
ALTER TABLE users
ADD COLUMN lineVerified BOOLEAN DEFAULT FALSE AFTER isVerified;

-- 創建用戶日誌表
CREATE TABLE IF NOT EXISTS userLogs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  userId INT NOT NULL,
  action ENUM('login', 'logout', 'profile_update') NOT NULL,
  oldData JSON NULL,
  newData JSON NULL,
  ipAddress VARCHAR(45) NULL,
  userAgent TEXT NULL,
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
);

-- 創建索引以提升查詢效能
CREATE INDEX idx_userLogs_userId ON userLogs(userId);
CREATE INDEX idx_userLogs_action ON userLogs(action);
CREATE INDEX idx_userLogs_createdAt ON userLogs(createdAt);
