# 🔐 驗證狀態問題修復

## 問題描述

用戶顯示"已存在"但"未完成驗證"，這是因為：

1. **LINE 登入用戶**被創建時 `isVerified: false`
2. **前端邏輯**只檢查 `isVerified` 欄位
3. **缺乏區分**：沒有區分 LINE 驗證和手機驗證

## 🔧 解決方案

### 1. 新增 `lineVerified` 欄位

在用戶模型中新增 `lineVerified` 欄位來區分兩種驗證狀態：

```sql
ALTER TABLE users 
ADD COLUMN lineVerified BOOLEAN DEFAULT FALSE AFTER isVerified;
```

### 2. 修改驗證邏輯

**後端修改**：
- LINE 登入用戶：`isVerified: false`, `lineVerified: true`
- 手機驗證用戶：`isVerified: true`, `lineVerified: false`
- 完全驗證用戶：`isVerified: true`, `lineVerified: true`

**前端修改**：
```typescript
const isPhoneVerified = !!userData.isVerified;
const isLineVerified = !!userData.lineVerified;
const isFullyVerified = isRegistered && (isPhoneVerified || isLineVerified);
```

### 3. 驗證狀態邏輯

| 情況 | isVerified | lineVerified | isFullyVerified | 狀態描述 |
|------|------------|--------------|-----------------|----------|
| 新 LINE 用戶 | false | true | true | 已驗證 (LINE) |
| 手機驗證用戶 | true | false | true | 已驗證 (手機) |
| 完全驗證用戶 | true | true | true | 已驗證 (完全) |
| 未驗證用戶 | false | false | false | 未驗證 |

## 📋 修改清單

### 後端修改：

1. **用戶模型** (`server/models/user.model.js`)：
   - 新增 `lineVerified` 欄位

2. **認證控制器** (`server/controllers/auth.controller.js`)：
   - LINE 登入時設置 `lineVerified: true`
   - 返回用戶資料包含 `lineVerified`

3. **用戶控制器** (`server/controllers/user.controller.js`)：
   - 用戶資料查詢包含 `lineVerified`

4. **資料庫遷移**：
   - SQL 腳本和 Node.js 腳本

### 前端修改：

1. **useAuth Hook** (`client/src/hooks/useAuth.ts`)：
   - 新增 `isLineVerified` 狀態
   - 修改 `isFullyVerified` 邏輯
   - 更新所有狀態設置

2. **UserStatus 介面**：
   - 新增 `isLineVerified: boolean`

## 🧪 測試方式

### 1. 執行資料庫遷移

```bash
cd server
node scripts/migrate-user-schema.js
```

### 2. 測試驗證狀態

```bash
cd server
node test-verification-status.js
```

### 3. 前端測試

```typescript
import { useAuth } from '@/hooks/useAuth';

const { isRegistered, isPhoneVerified, isLineVerified, isFullyVerified } = useAuth();

console.log('註冊狀態:', isRegistered);
console.log('手機驗證:', isPhoneVerified);
console.log('LINE 驗證:', isLineVerified);
console.log('完全驗證:', isFullyVerified);
```

## 🎯 預期結果

### LINE 登入用戶（Andy瑄）：

**修復前**：
```json
{
  "isVerified": false,
  "isFullyVerified": false,
  "狀態": "未完成驗證"
}
```

**修復後**：
```json
{
  "isVerified": false,
  "lineVerified": true,
  "isFullyVerified": true,
  "狀態": "已驗證 (LINE)"
}
```

### 用戶體驗改善：

1. **LINE 登入用戶**：立即顯示為"已驗證"
2. **手機驗證用戶**：顯示為"已驗證"
3. **未驗證用戶**：顯示為"未驗證"
4. **清楚區分**：可以知道是透過哪種方式驗證

## 🔍 除錯指南

### 檢查資料庫結構：

```sql
DESCRIBE users;
-- 應該看到 lineVerified 欄位
```

### 檢查用戶資料：

```sql
SELECT id, name, isVerified, lineVerified FROM users WHERE lineId = 'Udf723d52cde2495367205e5751fb8c8d';
-- Andy瑄 應該顯示：isVerified=0, lineVerified=1
```

### 檢查 API 響應：

```bash
curl -X POST http://localhost:5000/api/auth/line-login \
  -H "Content-Type: application/json" \
  -d '{"accessToken": "dev_token_andy"}'
```

應該返回：
```json
{
  "user": {
    "isVerified": false,
    "lineVerified": true
  }
}
```

## ✅ 驗證清單

- [ ] 資料庫遷移成功
- [ ] `lineVerified` 欄位已新增
- [ ] LINE 登入設置 `lineVerified: true`
- [ ] 前端正確處理驗證狀態
- [ ] Andy瑄 顯示為"已驗證"
- [ ] 測試腳本通過
- [ ] 用戶體驗改善

現在 LINE 登入用戶應該正確顯示為"已驗證"狀態！🎉
