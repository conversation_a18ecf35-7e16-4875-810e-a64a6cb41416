module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/utils/api.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$liff$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/liff.ts [app-ssr] (ecmascript)");
;
;
// API 基礎 URL
const API_BASE_URL = ("TURBOPACK compile-time value", "https://orangedrink-api.zeabur.app/api") || 'http://localhost:5000/api';
// 創建 axios 實例
const api = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json'
    }
});
// 創建不需要 JWT 驗證的 axios 實例
const publicApi = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json'
    }
});
// 請求攔截器：添加 token 到請求頭
api.interceptors.request.use((config)=>{
    const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$liff$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAccessToken"])();
    console.log(token);
    if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// 響應攔截器：處理錯誤
api.interceptors.response.use((response)=>{
    return response;
}, (error)=>{
    // 處理 401 未授權錯誤
    if (error.response && error.response.status === 401) {
        // 可以在這裡處理登出邏輯
        console.error('Unauthorized, please login again');
    }
    return Promise.reject(error);
});
// API 服務
const apiService = {
    // 身份驗證相關 API
    auth: {
        // LINE 登入
        lineLogin: async (accessToken)=>{
            // 使用 publicApi 避免自動添加 JWT token
            const response = await publicApi.post('/auth/line-login', {
                accessToken
            });
            return response.data;
        },
        // 註冊
        register: async (lineId, name, phone)=>{
            const response = await publicApi.post('/auth/register', {
                lineId,
                name,
                phone
            });
            return response.data;
        },
        // 發送手機驗證碼
        sendVerification: async (phone)=>{
            const response = await publicApi.post('/auth/send-verification', {
                phone
            });
            return response.data;
        },
        // 驗證手機驗證碼
        verifyPhone: async (phone, code)=>{
            const response = await publicApi.post('/auth/verify-phone', {
                phone,
                code
            });
            return response.data;
        },
        // 登出
        logout: async ()=>{
            const response = await api.post('/auth/logout');
            return response.data;
        },
        // 獲取用戶日誌
        getUserLogs: async (page = 1, limit = 20, action)=>{
            const params = new URLSearchParams({
                page: page.toString(),
                limit: limit.toString()
            });
            if (action) {
                params.append('action', action);
            }
            const response = await api.get(`/auth/logs?${params}`);
            return response.data;
        }
    },
    // 用戶相關 API
    user: {
        // 獲取用戶資料
        getProfile: async ()=>{
            const response = await api.get('/user/profile');
            return response.data;
        },
        // 更新用戶資料
        updateProfile: async (data)=>{
            const response = await api.put('/user/profile', data);
            return response.data;
        },
        // 獲取用戶訂單歷史
        getOrders: async ()=>{
            const response = await api.get('/user/orders');
            return response.data;
        }
    },
    // 產品相關 API
    products: {
        // 獲取所有產品
        getAll: async ()=>{
            const response = await api.get('/products');
            return response.data;
        },
        // 獲取產品分類
        getCategories: async ()=>{
            const response = await api.get('/products/categories');
            return response.data;
        },
        // 獲取特定分類的產品
        getByCategory: async (category)=>{
            const response = await api.get(`/products/category/${category}`);
            return response.data;
        },
        // 獲取特定產品詳情
        getById: async (id)=>{
            const response = await api.get(`/products/${id}`);
            return response.data;
        }
    },
    // 訂單相關 API
    orders: {
        // 創建新訂單
        create: async (orderData)=>{
            const response = await api.post('/orders', orderData);
            return response.data;
        },
        // 獲取訂單詳情
        getById: async (id)=>{
            const response = await api.get(`/orders/${id}`);
            return response.data;
        },
        // 取消訂單
        cancel: async (id)=>{
            const response = await api.put(`/orders/${id}/cancel`);
            return response.data;
        }
    }
};
const __TURBOPACK__default__export__ = apiService;
}}),
"[project]/src/utils/liff.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearAuthTokens": (()=>clearAuthTokens),
    "clearUserData": (()=>clearUserData),
    "closeLiff": (()=>closeLiff),
    "default": (()=>__TURBOPACK__default__export__),
    "getAccessToken": (()=>getAccessToken),
    "getIdToken": (()=>getIdToken),
    "getUserProfile": (()=>getUserProfile),
    "initializeLiff": (()=>initializeLiff),
    "isInLiffBrowser": (()=>isInLiffBrowser),
    "isLoggedIn": (()=>isLoggedIn),
    "login": (()=>login),
    "loginWithBackend": (()=>loginWithBackend),
    "logout": (()=>logout),
    "logoutWithCleanup": (()=>logoutWithCleanup),
    "openExternalLink": (()=>openExternalLink),
    "shareMessage": (()=>shareMessage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@line/liff/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/api.ts [app-ssr] (ecmascript)");
;
;
// LIFF ID
const LIFF_ID = ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : '2007460761-vMp0L0WA'; // 伺服器端
// 檢查是否為開發模式
const isDevMode = ()=>{
    return ("TURBOPACK compile-time value", "development") === 'development' && "undefined" !== 'undefined' && window.location.hostname === 'localhost';
};
const initializeLiff = async ()=>{
    try {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].init({
            liffId: LIFF_ID,
            withLoginOnExternalBrowser: true
        });
        console.log('LIFF initialization succeeded');
    } catch (error) {
        console.error('LIFF initialization failed', error);
        throw error;
    }
};
const isInLiffBrowser = ()=>{
    if ("TURBOPACK compile-time truthy", 1) return false;
    "TURBOPACK unreachable";
};
const isLoggedIn = ()=>{
    if ("TURBOPACK compile-time truthy", 1) return false;
    "TURBOPACK unreachable";
};
const getUserProfile = async ()=>{
    try {
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isLoggedIn()) {
            throw new Error('User is not logged in');
        }
        // 獲取用戶資料
        const profile = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getProfile();
        return {
            userId: profile.userId,
            displayName: profile.displayName,
            pictureUrl: profile.pictureUrl
        };
    } catch (error) {
        console.error('Failed to get user profile', error);
        throw error;
    }
};
const login = ()=>{
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isLoggedIn()) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].login();
    }
};
const loginWithBackend = async ()=>{
    try {
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isLoggedIn()) {
            throw new Error('User is not logged in to LINE');
        }
        // 取得 access token
        const accessToken = getAccessToken();
        if (!accessToken) {
            throw new Error('Unable to get access token');
        }
        // 調用後端 API 進行登入
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].auth.lineLogin(accessToken);
        console.log('Backend login successful:', response);
        return response;
    } catch (error) {
        console.error('Backend login failed:', error);
        throw error;
    }
};
const clearUserData = ()=>{
    try {
        // 清理 localStorage 中的用戶相關資料
        const keysToRemove = [
            'userProfile',
            'userPreferences',
            'cartItems',
            'lastOrderId',
            'userSettings'
        ];
        keysToRemove.forEach((key)=>{
            localStorage.removeItem(key);
        });
        // 清理 sessionStorage
        sessionStorage.clear();
        console.log('User data cleared successfully');
    } catch (error) {
        console.error('Error clearing user data:', error);
    }
};
const clearAuthTokens = ()=>{
    try {
        // 清理可能儲存在 localStorage 中的 token
        const tokenKeys = [
            'access_token',
            'refresh_token',
            'id_token',
            'liff_token',
            'auth_token'
        ];
        tokenKeys.forEach((key)=>{
            localStorage.removeItem(key);
            sessionStorage.removeItem(key);
        });
        console.log('Auth tokens cleared successfully');
    } catch (error) {
        console.error('Error clearing auth tokens:', error);
    }
};
const logout = ()=>{
    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isLoggedIn()) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].logout();
        window.location.reload();
    }
};
const logoutWithCleanup = async ()=>{
    try {
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isLoggedIn()) {
            console.warn('User is not logged in');
            return;
        }
        // 1. 先調用伺服器端登出 API（如果有 token）
        try {
            const token = getAccessToken();
            if (token) {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].auth.logout();
                console.log('Server logout successful');
            }
        } catch (apiError) {
            console.warn('Server logout failed, continuing with client logout:', apiError);
        // 即使伺服器登出失敗，也繼續執行客戶端登出
        }
        // 2. 清理本地資料和 token
        clearUserData();
        clearAuthTokens();
        // 3. 執行 LIFF 登出（這會清除 LIFF 的 access token）
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].logout();
        // 4. 短暫延遲後重新載入頁面，確保登出完成
        setTimeout(()=>{
            window.location.href = '/';
        }, 100);
    } catch (error) {
        console.error('Logout failed:', error);
        // 即使出錯也嘗試重新載入頁面
        window.location.reload();
    }
};
const closeLiff = ()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].closeWindow();
};
const getIdToken = ()=>{
    if ("TURBOPACK compile-time truthy", 1) return null;
    "TURBOPACK unreachable";
};
const getAccessToken = ()=>{
    if ("TURBOPACK compile-time truthy", 1) return null;
    "TURBOPACK unreachable";
};
const openExternalLink = (url)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].openWindow({
        url,
        external: true
    });
};
const shareMessage = async (messages)=>{
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isInClient()) {
        console.error('Share message is only available in LINE app');
        return;
    }
    try {
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].shareTargetPicker(messages);
    } catch (error) {
        console.error('Failed to share message', error);
        throw error;
    }
};
const __TURBOPACK__default__export__ = {
    initializeLiff,
    isInLiffBrowser,
    isLoggedIn,
    getUserProfile,
    login,
    loginWithBackend,
    logout,
    logoutWithCleanup,
    clearUserData,
    clearAuthTokens,
    closeLiff,
    getIdToken,
    getAccessToken,
    openExternalLink,
    shareMessage
};
}}),
"[project]/src/providers/LiffProvider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "LiffProvider": (()=>LiffProvider),
    "default": (()=>__TURBOPACK__default__export__),
    "useLiff": (()=>useLiff)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$line$2f$liff$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@line/liff/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$liff$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/liff.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
// 創建 LIFF 上下文
const LiffContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])({
    isInitialized: false,
    isLoggedIn: false,
    isInClient: false,
    userProfile: null,
    error: null,
    isDevelopmentMode: false
});
// 開發模式的模擬用戶資料
const mockUserProfile = {
    userId: 'Udf723d52cde2495367205e5751fb8c8d',
    displayName: 'Andy瑄',
    pictureUrl: 'https://cdn-icons-png.flaticon.com/512/3135/3135715.png'
};
const LiffProvider = ({ children })=>{
    // 判斷是否為開發環境
    const isDevelopment = ("TURBOPACK compile-time value", "development") === 'development';
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(isDevelopment);
    const [isInClient, setIsInClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [userProfile, setUserProfile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(("TURBOPACK compile-time truthy", 1) ? mockUserProfile : ("TURBOPACK unreachable", undefined));
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // 如果是開發模式且想要繞過 LIFF 登入，直接返回
        if ("TURBOPACK compile-time truthy", 1) {
            console.log('開發模式：已繞過 LIFF 登入');
            setIsInitialized(true);
            return;
        }
        "TURBOPACK unreachable";
    }, [
        isDevelopment
    ]);
    // 提供 LIFF 上下文
    const value = {
        isInitialized,
        isLoggedIn: ("TURBOPACK compile-time truthy", 1) ? true : ("TURBOPACK unreachable", undefined),
        isInClient,
        userProfile,
        error,
        isDevelopmentMode: isDevelopment && ("TURBOPACK compile-time value", "true") === 'true'
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(LiffContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/providers/LiffProvider.tsx",
        lineNumber: 92,
        columnNumber: 10
    }, this);
};
const useLiff = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(LiffContext);
const __TURBOPACK__default__export__ = LiffProvider;
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__51fda869._.js.map