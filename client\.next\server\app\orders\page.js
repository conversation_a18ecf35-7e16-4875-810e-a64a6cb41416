(()=>{var e={};e.id=778,e.ids=[778],e.modules={440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},769:(e,t,s)=>{"use strict";s.d(t,{A:()=>x});var r=s(687);s(3210);var a=s(6189),i=s(584),l=s(7979),n=s(3166),d=s(3613),c=s(8869),o=s(5814),u=s.n(o);function x({children:e,requireAuth:t=!0,requireVerification:s=!0}){(0,a.useRouter)();let{isInitialized:o,isLoggedIn:x}=(0,i.x)(),{isFullyVerified:m,loading:p}=(0,l.h)();return!o||p?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center gradient-bg",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 gradient-orange rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse",children:(0,r.jsx)(n.A,{className:"w-8 h-8 text-white"})}),(0,r.jsx)("p",{className:"text-lg font-medium text-gray-700",children:"載入中..."}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"正在檢查會員狀態"})]})}):t&&!x?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center gradient-bg",children:(0,r.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-3xl shadow-warm p-8 max-w-md mx-4 text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(d.A,{className:"w-8 h-8 text-red-500"})}),(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-2",children:"需要登入"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"請先登入以使用此功能"}),(0,r.jsx)(u(),{href:"/",className:"w-full py-3 px-6 gradient-green text-white font-semibold rounded-2xl btn-hover shadow-warm inline-block",children:"返回首頁"})]})}):s&&x&&!m?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center gradient-bg",children:(0,r.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-3xl shadow-warm p-8 max-w-md mx-4 text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(c.A,{className:"w-8 h-8 text-blue-500"})}),(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-2",children:"需要會員驗證"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"請先完成會員註冊和手機驗證以使用此功能"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(u(),{href:"/register",className:"w-full py-3 px-6 bg-blue-500 text-white font-semibold rounded-2xl btn-hover shadow-warm inline-block",children:"立即註冊"}),(0,r.jsx)(u(),{href:"/",className:"w-full py-3 px-6 bg-gray-200 text-gray-700 font-semibold rounded-2xl btn-hover inline-block",children:"返回首頁"})]})]})}):(0,r.jsx)(r.Fragment,{children:e})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1057:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("shopping-bag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},2412:e=>{"use strict";e.exports=require("assert")},2595:(e,t,s)=>{Promise.resolve().then(s.bind(s,4346))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3613:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3649:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4346:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\0_project\\\\augment-projects\\\\cocodrink-line\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\orders\\page.tsx","default")},4735:e=>{"use strict";e.exports=require("events")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},5694:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>o,routeModule:()=>x,tree:()=>c});var r=s(5239),a=s(8088),i=s(8170),l=s.n(i),n=s(893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(t,d);let c={children:["",{children:["orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,4346)),"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\orders\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\orders\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/orders/page",pathname:"/orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},6189:(e,t,s)=>{"use strict";var r=s(5773);s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}})},7796:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(687);function a({item:e,onEditItem:t,canEdit:s=!1}){return(0,r.jsx)("div",{className:"border-l-4 border-purple-200 pl-4 py-3 bg-gray-50 rounded-r-lg",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[s&&t?(0,r.jsxs)("button",{onClick:()=>t(e),className:"font-medium text-purple-600 hover:text-purple-800 hover:underline transition-colors",children:[e.product?.name||"商品"," \xd7 ",e.quantity]}):(0,r.jsxs)("span",{className:"font-medium text-gray-800",children:[e.product?.name||"商品"," \xd7 ",e.quantity]}),s&&(0,r.jsx)("span",{className:"text-xs text-gray-400",children:"(點擊修改)"})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["原價: NT$ ",e.product?.price||0]}),(()=>{if(!e.customizations)return null;let t=[];return e.customizations.sugar&&t.push((0,r.jsxs)("span",{className:"text-xs text-orange-600",children:["\uD83C\uDF6F ",e.customizations.sugar]},"sugar")),e.customizations.ice&&t.push((0,r.jsxs)("span",{className:"text-xs text-blue-600",children:["\uD83E\uDDCA ",e.customizations.ice]},"ice")),t.length>0?(0,r.jsx)("div",{className:"flex items-center gap-3 mt-1",children:t}):null})(),(()=>{if(!e.customizations?.toppings||0===e.customizations.toppings.length)return null;let t=e=>({珍珠:10,椰果:10,鮮奶油:15,巧克力片:10,額外奶泡:10,布丁:12,仙草:8})[e]||10;return(0,r.jsx)("div",{className:"mt-2 space-y-1",children:e.customizations.toppings.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,r.jsxs)("span",{className:"text-green-600 flex items-center gap-1",children:[(0,r.jsx)("span",{children:"\uD83E\uDD64"}),(0,r.jsx)("span",{children:e})]}),(0,r.jsxs)("span",{className:"text-orange-500 font-medium",children:["+NT$ ",t(e)]})]},s))})})()]}),(0,r.jsxs)("div",{className:"text-right ml-4",children:[(0,r.jsxs)("div",{className:"font-semibold text-gray-800",children:["NT$ ",e.price*e.quantity]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["單價: NT$ ",e.price]})]})]})})}},7910:e=>{"use strict";e.exports=require("stream")},7979:(e,t,s)=>{"use strict";s.d(t,{h:()=>n});var r=s(3210),a=s(584),i=s(7327),l=s(2013);let n=()=>{let{isInitialized:e,isLoggedIn:t,userProfile:s}=(0,a.x)(),[n,d]=(0,r.useState)({isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null,loading:!0,error:null}),c=async()=>{if(!e||!t||!s)return void d(e=>({...e,loading:!1,isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null}));try{if(d(e=>({...e,loading:!0,error:null})),!i.b.hasValidToken()&&(console.log("No valid JWT token, performing backend login..."),!await (0,l.UK)()))throw Error("Failed to obtain JWT token");let e=await i.A.user.getProfile();if(e&&e.id){let t=!!e.id,s=!!e.isVerified,r=!!e.lineVerified,a=t&&s&&r;d({isRegistered:t,isPhoneVerified:s,isLineVerified:r,isFullyVerified:a,userInfo:e,loading:!1,error:null})}else d({isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null,loading:!1,error:null})}catch(e){console.error("檢查會員狀態失敗:",e),e.response?.status===404?d({isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null,loading:!1,error:null}):d(t=>({...t,loading:!1,error:e.response?.data?.message||"檢查會員狀態失敗"}))}};return(0,r.useEffect)(()=>{e&&c()},[e,t,s]),{...n,refreshUserStatus:()=>{c()}}}},8251:(e,t,s)=>{Promise.resolve().then(s.bind(s,9638))},8354:e=>{"use strict";e.exports=require("util")},8559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9638:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var r=s(687),a=s(769),i=s(584),l=s(5814),n=s.n(l),d=s(2688);let c=(0,d.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var o=s(3166),u=s(5336);let x=(0,d.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var m=s(1057),p=s(8559),h=s(3649),g=s(3210),f=s(7327),b=s(7796);function j(){let{userProfile:e}=(0,i.x)(),[t,s]=(0,g.useState)("all"),[l,d]=(0,g.useState)([]),[j,y]=(0,g.useState)(!0),[N,v]=(0,g.useState)(null),[w,k]=(0,g.useState)(null),[A,q]=(0,g.useState)(!1),[P,z]=(0,g.useState)(null),[_,V]=(0,g.useState)(""),C=[{id:"all",name:"全部",color:"gray"},{id:"pending",name:"處理中",color:"yellow"},{id:"processing",name:"製作中",color:"blue"},{id:"completed",name:"已完成",color:"green"},{id:"cancelled",name:"已取消",color:"red"}],T=async()=>{try{y(!0);let e=await f.A.user.getOrders();d(e)}catch(e){console.error("Failed to fetch orders:",e),v("無法載入訂單列表")}finally{y(!1)}},D=e=>{z(e),q(!0),V("")},S=async()=>{if(P){k(P.id);try{"pending"===P.status?(await f.A.orders.cancel(P.id),alert("訂單已成功取消")):"processing"===P.status&&(await f.A.orders.requestCancel(P.id,_),alert("取消請求已發送，等待管理員確認")),await T()}catch(e){console.error("Cancel order failed:",e),alert(e.response?.data?.message||"取消訂單失敗，請稍後再試")}finally{k(null),q(!1),z(null),V("")}}},F=e=>"pending"===e||"processing"===e,M=e=>{switch(e){case"pending":return(0,r.jsx)(c,{className:"w-5 h-5 text-yellow-500"});case"processing":return(0,r.jsx)(o.A,{className:"w-5 h-5 text-blue-500"});case"completed":return(0,r.jsx)(u.A,{className:"w-5 h-5 text-green-500"});case"cancelled":return(0,r.jsx)(x,{className:"w-5 h-5 text-red-500"});default:return(0,r.jsx)(c,{className:"w-5 h-5 text-gray-500"})}},R=e=>{let t=C.find(t=>t.id===e);return t?t.name:e},E=e=>{let t=C.find(t=>t.id===e);return t?t.color:"gray"},L="all"===t?l:l.filter(e=>e.status===t),$=e=>{let t=new Date(e);return{date:t.toLocaleDateString("zh-TW"),time:t.toLocaleTimeString("zh-TW",{hour:"2-digit",minute:"2-digit"})}};return j?(0,r.jsx)(a.A,{requireAuth:!0,requireVerification:!0,children:(0,r.jsx)("div",{className:"min-h-screen gradient-bg flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"載入訂單中..."})]})})}):N?(0,r.jsx)(a.A,{requireAuth:!0,requireVerification:!0,children:(0,r.jsx)("div",{className:"min-h-screen gradient-bg flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(m.A,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-red-500 mb-4",children:N}),(0,r.jsx)("button",{onClick:T,className:"px-6 py-3 bg-purple-500 text-white rounded-full hover:bg-purple-600 transition-colors",children:"重新載入"})]})})}):(0,r.jsx)(a.A,{requireAuth:!0,requireVerification:!0,children:(0,r.jsxs)("div",{className:"min-h-screen gradient-bg",children:[(0,r.jsx)("header",{className:"bg-white/80 backdrop-blur-md shadow-soft sticky top-0 z-50",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(n(),{href:"/",className:"p-2 bg-purple-100 text-purple-600 rounded-full hover:bg-purple-200 transition-colors",children:(0,r.jsx)(p.A,{className:"w-5 h-5"})}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(m.A,{className:"w-6 h-6 text-purple-500"}),(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-800",children:"我的訂單"})]})]}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:e?.displayName})]})}),(0,r.jsxs)("main",{className:"container mx-auto px-4 py-6",children:[(0,r.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"訂單記錄"}),(0,r.jsx)("p",{className:"text-gray-600",children:"查看您的訂單狀態和歷史記錄"})]}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)("div",{className:"flex gap-2 overflow-x-auto pb-2",children:C.map(e=>(0,r.jsx)("button",{onClick:()=>s(e.id),className:`px-4 py-2 rounded-full whitespace-nowrap transition-colors ${t===e.id?"bg-purple-500 text-white":"bg-white text-gray-600 hover:bg-purple-100"}`,children:e.name},e.id))})}),(0,r.jsx)("div",{className:"space-y-4",children:L.map(e=>{let{date:t,time:s}=$(e.createdAt);return(0,r.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"font-bold text-gray-800",children:["訂單 #",e.id]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[t," ",s]}),"scheduled"===e.orderType&&e.scheduledTime&&(0,r.jsxs)("p",{className:"text-sm text-orange-600",children:["預約時間: ",$(e.scheduledTime).date," ",$(e.scheduledTime).time]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[M(e.status),(0,r.jsx)("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${"yellow"===E(e.status)?"bg-yellow-100 text-yellow-800":"blue"===E(e.status)?"bg-blue-100 text-blue-800":"green"===E(e.status)?"bg-green-100 text-green-800":"red"===E(e.status)?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:R(e.status)})]})]}),(0,r.jsx)("div",{className:"space-y-3 mb-4",children:e.items&&e.items.map((e,t)=>(0,r.jsx)(b.A,{item:e,canEdit:!1},t))}),e.note&&(0,r.jsx)("div",{className:"mb-4 p-3 bg-gray-50 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,r.jsx)("strong",{children:"備註:"})," ",e.note]})}),(0,r.jsxs)("div",{className:"border-t pt-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,r.jsx)("span",{className:"font-bold text-gray-800",children:"總計"}),(0,r.jsxs)("span",{className:"font-bold text-purple-500 text-lg",children:["NT$ ",e.totalPrice]})]}),F(e.status)&&(0,r.jsx)("button",{onClick:()=>D(e),disabled:w===e.id,className:"w-full py-2 px-4 bg-red-100 text-red-600 rounded-lg hover:bg-red-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:w===e.id?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-red-600 border-t-transparent rounded-full animate-spin"}),"處理中..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x,{className:"w-4 h-4"}),"pending"===e.status?"取消訂單":"申請取消"]})})]})]},e.id)})}),0===L.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(m.A,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-500",children:"暫無訂單記錄"}),(0,r.jsx)(n(),{href:"/products",className:"inline-block mt-4 px-6 py-3 bg-purple-500 text-white rounded-full hover:bg-purple-600 transition-colors",children:"開始點餐"})]})]}),A&&P&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-3xl shadow-warm max-w-md w-full p-6",children:[(0,r.jsxs)("div",{className:"text-center mb-6",children:[(0,r.jsx)(h.A,{className:"w-16 h-16 text-orange-500 mx-auto mb-4"}),(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-2",children:"pending"===P.status?"確認取消訂單":"申請取消訂單"}),(0,r.jsxs)("p",{className:"text-gray-600",children:["訂單 #",P.id]})]}),"pending"===P.status?(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)("p",{className:"text-gray-700 text-center",children:"此訂單目前處於處理中狀態，可以直接取消。"})}):(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("p",{className:"text-gray-700 mb-3",children:"此訂單正在製作中，需要管理員確認才能取消。請說明取消原因："}),(0,r.jsx)("textarea",{value:_,onChange:e=>V(e.target.value),placeholder:"請輸入取消原因...",className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-red-500",rows:3,required:!0})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("button",{onClick:()=>{q(!1),z(null),V("")},className:"flex-1 py-3 px-4 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors",children:"取消"}),(0,r.jsx)("button",{onClick:S,disabled:"processing"===P.status&&!_.trim(),className:"flex-1 py-3 px-4 bg-red-500 text-white rounded-xl hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"pending"===P.status?"確認取消":"發送申請"})]})]})})]})})}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,173,248,847],()=>s(5694));module.exports=r})();