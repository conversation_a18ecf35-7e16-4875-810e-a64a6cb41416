{"version": 3, "sources": [], "sections": [{"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/0_project/augment-projects/cocodrink-line/client/src/utils/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { getAccessToken } from './liff';\n\n// API 基礎 URL\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\n// 創建 axios 實例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 創建不需要 JWT 驗證的 axios 實例\nconst publicApi = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 請求攔截器：添加 token 到請求頭\napi.interceptors.request.use(\n  (config) => {\n    const token = getAccessToken();\n    console.log(token);\n    if (token) {\n      config.headers['Authorization'] = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 響應攔截器：處理錯誤\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    // 處理 401 未授權錯誤\n    if (error.response && error.response.status === 401) {\n      // 可以在這裡處理登出邏輯\n      console.error('Unauthorized, please login again');\n    }\n    return Promise.reject(error);\n  }\n);\n\n// API 服務\nconst apiService = {\n  // 身份驗證相關 API\n  auth: {\n    // LINE 登入\n    lineLogin: async (accessToken: string) => {\n      // 使用 publicApi 避免自動添加 JWT token\n      const response = await publicApi.post('/auth/line-login', { accessToken });\n      return response.data;\n    },\n\n    // 註冊\n    register: async (lineId: string, name: string, phone: string) => {\n      const response = await publicApi.post('/auth/register', { lineId, name, phone });\n      return response.data;\n    },\n\n    // 發送手機驗證碼\n    sendVerification: async (phone: string) => {\n      const response = await publicApi.post('/auth/send-verification', { phone });\n      return response.data;\n    },\n\n    // 驗證手機驗證碼\n    verifyPhone: async (phone: string, code: string) => {\n      const response = await publicApi.post('/auth/verify-phone', { phone, code });\n      return response.data;\n    },\n\n    // 登出\n    logout: async () => {\n      const response = await api.post('/auth/logout');\n      return response.data;\n    },\n\n    // 獲取用戶日誌\n    getUserLogs: async (page = 1, limit = 20, action?: string) => {\n      const params = new URLSearchParams({\n        page: page.toString(),\n        limit: limit.toString(),\n      });\n\n      if (action) {\n        params.append('action', action);\n      }\n\n      const response = await api.get(`/auth/logs?${params}`);\n      return response.data;\n    },\n  },\n\n  // 用戶相關 API\n  user: {\n    // 獲取用戶資料\n    getProfile: async () => {\n      const response = await api.get('/user/profile');\n      return response.data;\n    },\n\n    // 更新用戶資料\n    updateProfile: async (data: any) => {\n      const response = await api.put('/user/profile', data);\n      return response.data;\n    },\n\n    // 獲取用戶訂單歷史\n    getOrders: async () => {\n      const response = await api.get('/user/orders');\n      return response.data;\n    },\n  },\n\n  // 產品相關 API\n  products: {\n    // 獲取所有產品\n    getAll: async () => {\n      const response = await api.get('/products');\n      return response.data;\n    },\n\n    // 獲取產品分類\n    getCategories: async () => {\n      const response = await api.get('/products/categories');\n      return response.data;\n    },\n\n    // 獲取特定分類的產品\n    getByCategory: async (category: string) => {\n      const response = await api.get(`/products/category/${category}`);\n      return response.data;\n    },\n\n    // 獲取特定產品詳情\n    getById: async (id: number) => {\n      const response = await api.get(`/products/${id}`);\n      return response.data;\n    },\n  },\n\n  // 訂單相關 API\n  orders: {\n    // 創建新訂單\n    create: async (orderData: any) => {\n      const response = await api.post('/orders', orderData);\n      return response.data;\n    },\n\n    // 獲取訂單詳情\n    getById: async (id: number) => {\n      const response = await api.get(`/orders/${id}`);\n      return response.data;\n    },\n\n    // 取消訂單\n    cancel: async (id: number) => {\n      const response = await api.put(`/orders/${id}/cancel`);\n      return response.data;\n    },\n  },\n};\n\nexport default apiService;\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,aAAa;AACb,MAAM,eAAe,8EAAmC;AAExD,cAAc;AACd,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,yBAAyB;AACzB,MAAM,YAAY,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,sBAAsB;AACtB,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAC1B,CAAC;IACC,MAAM,QAAQ,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD;IAC3B,QAAQ,GAAG,CAAC;IACZ,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IACrD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,aAAa;AACb,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC;IACC,OAAO;AACT,GACA,CAAC;IACC,eAAe;IACf,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;QACnD,cAAc;QACd,QAAQ,KAAK,CAAC;IAChB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,SAAS;AACT,MAAM,aAAa;IACjB,aAAa;IACb,MAAM;QACJ,UAAU;QACV,WAAW,OAAO;YAChB,gCAAgC;YAChC,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,oBAAoB;gBAAE;YAAY;YACxE,OAAO,SAAS,IAAI;QACtB;QAEA,KAAK;QACL,UAAU,OAAO,QAAgB,MAAc;YAC7C,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,kBAAkB;gBAAE;gBAAQ;gBAAM;YAAM;YAC9E,OAAO,SAAS,IAAI;QACtB;QAEA,UAAU;QACV,kBAAkB,OAAO;YACvB,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,2BAA2B;gBAAE;YAAM;YACzE,OAAO,SAAS,IAAI;QACtB;QAEA,UAAU;QACV,aAAa,OAAO,OAAe;YACjC,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,sBAAsB;gBAAE;gBAAO;YAAK;YAC1E,OAAO,SAAS,IAAI;QACtB;QAEA,KAAK;QACL,QAAQ;YACN,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC;YAChC,OAAO,SAAS,IAAI;QACtB;QAEA,SAAS;QACT,aAAa,OAAO,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE;YACxC,MAAM,SAAS,IAAI,gBAAgB;gBACjC,MAAM,KAAK,QAAQ;gBACnB,OAAO,MAAM,QAAQ;YACvB;YAEA,IAAI,QAAQ;gBACV,OAAO,MAAM,CAAC,UAAU;YAC1B;YAEA,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ;YACrD,OAAO,SAAS,IAAI;QACtB;IACF;IAEA,WAAW;IACX,MAAM;QACJ,SAAS;QACT,YAAY;YACV,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;YAC/B,OAAO,SAAS,IAAI;QACtB;QAEA,SAAS;QACT,eAAe,OAAO;YACpB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,iBAAiB;YAChD,OAAO,SAAS,IAAI;QACtB;QAEA,WAAW;QACX,WAAW;YACT,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;YAC/B,OAAO,SAAS,IAAI;QACtB;IACF;IAEA,WAAW;IACX,UAAU;QACR,SAAS;QACT,QAAQ;YACN,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;YAC/B,OAAO,SAAS,IAAI;QACtB;QAEA,SAAS;QACT,eAAe;YACb,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;YAC/B,OAAO,SAAS,IAAI;QACtB;QAEA,YAAY;QACZ,eAAe,OAAO;YACpB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,mBAAmB,EAAE,UAAU;YAC/D,OAAO,SAAS,IAAI;QACtB;QAEA,WAAW;QACX,SAAS,OAAO;YACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;YAChD,OAAO,SAAS,IAAI;QACtB;IACF;IAEA,WAAW;IACX,QAAQ;QACN,QAAQ;QACR,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;YAC3C,OAAO,SAAS,IAAI;QACtB;QAEA,SAAS;QACT,SAAS,OAAO;YACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,IAAI;YAC9C,OAAO,SAAS,IAAI;QACtB;QAEA,OAAO;QACP,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC;YACrD,OAAO,SAAS,IAAI;QACtB;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/0_project/augment-projects/cocodrink-line/client/src/utils/liff-dev.ts"], "sourcesContent": ["// 開發模式的 LIFF 模擬器\nimport apiService from './api';\n\n// 模擬的 LIFF 用戶資料\nconst mockUsers = [\n  {\n    accessToken: 'dev_token_1',\n    profile: {\n      userId: 'U1234567890abcdef1234567890abcdef',\n      displayName: '測試用戶一',\n      pictureUrl: 'https://profile.line-scdn.net/0h1234567890abcdef_large'\n    }\n  },\n  {\n    accessToken: 'dev_token_2',\n    profile: {\n      userId: 'U2345678901bcdef12345678901bcdef1',\n      displayName: '測試用戶二',\n      pictureUrl: 'https://profile.line-scdn.net/0h2345678901bcdef_large'\n    }\n  },\n  {\n    accessToken: 'dev_token_3',\n    profile: {\n      userId: 'U3456789012cdef123456789012cdef12',\n      displayName: '測試用戶三',\n      pictureUrl: null\n    }\n  }\n];\n\n// 開發模式狀態\nlet devModeState = {\n  isLoggedIn: false,\n  currentUser: null as any,\n  isInClient: true // 模擬在 LINE 應用內\n};\n\n// 開發模式的 LIFF 模擬器\nexport const liffDevMock = {\n  // 初始化\n  init: async (config: any) => {\n    console.log('🔧 Dev Mode: LIFF initialized with config:', config);\n    return Promise.resolve();\n  },\n\n  // 檢查是否已登入\n  isLoggedIn: () => {\n    return devModeState.isLoggedIn;\n  },\n\n  // 檢查是否在 LINE 應用內\n  isInClient: () => {\n    return devModeState.isInClient;\n  },\n\n  // 模擬登入\n  login: (userIndex = 0) => {\n    const user = mockUsers[userIndex] || mockUsers[0];\n    devModeState.isLoggedIn = true;\n    devModeState.currentUser = user;\n    console.log('🔧 Dev Mode: User logged in:', user.profile.displayName);\n    \n    // 儲存到 localStorage 以便重新載入後保持狀態\n    localStorage.setItem('dev_liff_user', JSON.stringify(user));\n    localStorage.setItem('dev_liff_logged_in', 'true');\n  },\n\n  // 模擬登出\n  logout: () => {\n    devModeState.isLoggedIn = false;\n    devModeState.currentUser = null;\n    console.log('🔧 Dev Mode: User logged out');\n    \n    // 清除 localStorage\n    localStorage.removeItem('dev_liff_user');\n    localStorage.removeItem('dev_liff_logged_in');\n  },\n\n  // 取得 access token\n  getAccessToken: () => {\n    if (!devModeState.isLoggedIn || !devModeState.currentUser) {\n      return null;\n    }\n    return devModeState.currentUser.accessToken;\n  },\n\n  // 取得用戶資料\n  getProfile: async () => {\n    if (!devModeState.isLoggedIn || !devModeState.currentUser) {\n      throw new Error('User is not logged in');\n    }\n    return devModeState.currentUser.profile;\n  },\n\n  // 取得 ID Token (模擬)\n  getIDToken: () => {\n    if (!devModeState.isLoggedIn) {\n      return null;\n    }\n    // 返回一個模擬的 JWT token\n    return 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2FjY2Vzcy5saW5lLm1lIiwic3ViIjoiVTEyMzQ1Njc4OTBhYmNkZWYxMjM0NTY3ODkwYWJjZGVmIiwiYXVkIjoiMTIzNDU2Nzg5MCIsImV4cCI6MTYzMjQ2NzIwMCwiaWF0IjoxNjMyNDYzNjAwLCJuYW1lIjoi5ris6Kmm55So5oi25LiAIiwicGljdHVyZSI6Imh0dHBzOi8vcHJvZmlsZS5saW5lLXNjZG4ubmV0LzBoMTIzNDU2Nzg5MGFiY2RlZl9sYXJnZSJ9.mock_signature';\n  },\n\n  // 關閉視窗\n  closeWindow: () => {\n    console.log('🔧 Dev Mode: Close window called');\n  },\n\n  // 恢復狀態（從 localStorage）\n  restoreState: () => {\n    const savedUser = localStorage.getItem('dev_liff_user');\n    const isLoggedIn = localStorage.getItem('dev_liff_logged_in') === 'true';\n    \n    if (savedUser && isLoggedIn) {\n      devModeState.currentUser = JSON.parse(savedUser);\n      devModeState.isLoggedIn = true;\n      console.log('🔧 Dev Mode: State restored for user:', devModeState.currentUser.profile.displayName);\n    }\n  },\n\n  // 切換用戶（開發用）\n  switchUser: (userIndex: number) => {\n    if (userIndex >= 0 && userIndex < mockUsers.length) {\n      liffDevMock.login(userIndex);\n      window.location.reload(); // 重新載入以更新狀態\n    }\n  },\n\n  // 取得所有可用的測試用戶\n  getAvailableUsers: () => {\n    return mockUsers.map((user, index) => ({\n      index,\n      name: user.profile.displayName,\n      userId: user.profile.userId\n    }));\n  }\n};\n\n// 開發模式的完整登入流程\nexport const devLoginWithBackend = async (userIndex = 0) => {\n  try {\n    // 模擬 LIFF 登入\n    liffDevMock.login(userIndex);\n    \n    // 取得 access token\n    const accessToken = liffDevMock.getAccessToken();\n    if (!accessToken) {\n      throw new Error('Unable to get access token');\n    }\n\n    // 調用後端 API 進行登入\n    const response = await apiService.auth.lineLogin(accessToken);\n    \n    console.log('🔧 Dev Mode: Backend login successful:', response);\n    return response;\n  } catch (error) {\n    console.error('🔧 Dev Mode: Backend login failed:', error);\n    throw error;\n  }\n};\n\n// 初始化開發模式\nexport const initDevMode = () => {\n  // 恢復之前的狀態\n  liffDevMock.restoreState();\n  \n  // 在開發工具中添加全域變數以便調試\n  if (typeof window !== 'undefined') {\n    (window as any).liffDev = liffDevMock;\n    (window as any).devLogin = devLoginWithBackend;\n    \n    console.log('🔧 Dev Mode initialized!');\n    console.log('Available commands:');\n    console.log('- window.liffDev.switchUser(0-2): 切換測試用戶');\n    console.log('- window.devLogin(userIndex): 執行完整登入流程');\n    console.log('- window.liffDev.getAvailableUsers(): 查看可用用戶');\n  }\n};\n\nexport default liffDevMock;\n"], "names": [], "mappings": "AAAA,iBAAiB;;;;;;;AACjB;;AAEA,gBAAgB;AAChB,MAAM,YAAY;IAChB;QACE,aAAa;QACb,SAAS;YACP,QAAQ;YACR,aAAa;YACb,YAAY;QACd;IACF;IACA;QACE,aAAa;QACb,SAAS;YACP,QAAQ;YACR,aAAa;YACb,YAAY;QACd;IACF;IACA;QACE,aAAa;QACb,SAAS;YACP,QAAQ;YACR,aAAa;YACb,YAAY;QACd;IACF;CACD;AAED,SAAS;AACT,IAAI,eAAe;IACjB,YAAY;IACZ,aAAa;IACb,YAAY,KAAK,eAAe;AAClC;AAGO,MAAM,cAAc;IACzB,MAAM;IACN,MAAM,OAAO;QACX,QAAQ,GAAG,CAAC,8CAA8C;QAC1D,OAAO,QAAQ,OAAO;IACxB;IAEA,UAAU;IACV,YAAY;QACV,OAAO,aAAa,UAAU;IAChC;IAEA,iBAAiB;IACjB,YAAY;QACV,OAAO,aAAa,UAAU;IAChC;IAEA,OAAO;IACP,OAAO,CAAC,YAAY,CAAC;QACnB,MAAM,OAAO,SAAS,CAAC,UAAU,IAAI,SAAS,CAAC,EAAE;QACjD,aAAa,UAAU,GAAG;QAC1B,aAAa,WAAW,GAAG;QAC3B,QAAQ,GAAG,CAAC,gCAAgC,KAAK,OAAO,CAAC,WAAW;QAEpE,+BAA+B;QAC/B,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;QACrD,aAAa,OAAO,CAAC,sBAAsB;IAC7C;IAEA,OAAO;IACP,QAAQ;QACN,aAAa,UAAU,GAAG;QAC1B,aAAa,WAAW,GAAG;QAC3B,QAAQ,GAAG,CAAC;QAEZ,kBAAkB;QAClB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;IAC1B;IAEA,kBAAkB;IAClB,gBAAgB;QACd,IAAI,CAAC,aAAa,UAAU,IAAI,CAAC,aAAa,WAAW,EAAE;YACzD,OAAO;QACT;QACA,OAAO,aAAa,WAAW,CAAC,WAAW;IAC7C;IAEA,SAAS;IACT,YAAY;QACV,IAAI,CAAC,aAAa,UAAU,IAAI,CAAC,aAAa,WAAW,EAAE;YACzD,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,aAAa,WAAW,CAAC,OAAO;IACzC;IAEA,mBAAmB;IACnB,YAAY;QACV,IAAI,CAAC,aAAa,UAAU,EAAE;YAC5B,OAAO;QACT;QACA,oBAAoB;QACpB,OAAO;IACT;IAEA,OAAO;IACP,aAAa;QACX,QAAQ,GAAG,CAAC;IACd;IAEA,uBAAuB;IACvB,cAAc;QACZ,MAAM,YAAY,aAAa,OAAO,CAAC;QACvC,MAAM,aAAa,aAAa,OAAO,CAAC,0BAA0B;QAElE,IAAI,aAAa,YAAY;YAC3B,aAAa,WAAW,GAAG,KAAK,KAAK,CAAC;YACtC,aAAa,UAAU,GAAG;YAC1B,QAAQ,GAAG,CAAC,yCAAyC,aAAa,WAAW,CAAC,OAAO,CAAC,WAAW;QACnG;IACF;IAEA,YAAY;IACZ,YAAY,CAAC;QACX,IAAI,aAAa,KAAK,YAAY,UAAU,MAAM,EAAE;YAClD,YAAY,KAAK,CAAC;YAClB,OAAO,QAAQ,CAAC,MAAM,IAAI,YAAY;QACxC;IACF;IAEA,cAAc;IACd,mBAAmB;QACjB,OAAO,UAAU,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBACrC;gBACA,MAAM,KAAK,OAAO,CAAC,WAAW;gBAC9B,QAAQ,KAAK,OAAO,CAAC,MAAM;YAC7B,CAAC;IACH;AACF;AAGO,MAAM,sBAAsB,OAAO,YAAY,CAAC;IACrD,IAAI;QACF,aAAa;QACb,YAAY,KAAK,CAAC;QAElB,kBAAkB;QAClB,MAAM,cAAc,YAAY,cAAc;QAC9C,IAAI,CAAC,aAAa;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,gBAAgB;QAChB,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC;QAEjD,QAAQ,GAAG,CAAC,0CAA0C;QACtD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,MAAM;IACR;AACF;AAGO,MAAM,cAAc;IACzB,UAAU;IACV,YAAY,YAAY;IAExB,mBAAmB;IACnB,uCAAmC;;IASnC;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 445, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/0_project/augment-projects/cocodrink-line/client/src/utils/liff.ts"], "sourcesContent": ["import liff from '@line/liff';\nimport apiService from './api';\nimport { liffDevMock, devLoginWithBackend } from './liff-dev';\n\n// LIFF ID\nconst LIFF_ID = typeof window !== 'undefined'\n  ? process.env.NEXT_PUBLIC_LIFF_ID || '2007460761-vMp0L0WA'  // 客戶端\n  : '2007460761-vMp0L0WA';  // 伺服器端\n\n// 檢查是否為開發模式\nconst isDevMode = () => {\n  return process.env.NODE_ENV === 'development' &&\n         typeof window !== 'undefined' &&\n         window.location.hostname === 'localhost';\n};\n\n// LIFF 初始化\nexport const initializeLiff = async (): Promise<void> => {\n  try {\n    // 開發模式：使用模擬器\n    if (isDevMode()) {\n      console.log('🔧 Development mode: Using LIFF mock');\n      await liffDevMock.init({ liffId: LIFF_ID });\n      return;\n    }\n\n    if (!LIFF_ID) {\n      throw new Error('LIFF ID is not defined');\n    }\n\n    await liff.init({\n      liffId: LIFF_ID,\n      withLoginOnExternalBrowser: true, // 在外部瀏覽器中自動登入\n    });\n    console.log('LIFF initialization succeeded');\n  } catch (error) {\n    console.error('LIFF initialization failed', error);\n    throw error;\n  }\n};\n\n// 檢查是否在 LIFF 環境中\nexport const isInLiffBrowser = (): boolean => {\n  if (typeof window === 'undefined') return false;\n\n  // 開發模式\n  if (isDevMode()) {\n    return liffDevMock.isInClient();\n  }\n\n  return liff.isInClient();\n};\n\n// 檢查用戶是否已登入\nexport const isLoggedIn = (): boolean => {\n  if (typeof window === 'undefined') return false;\n\n  // 開發模式\n  if (isDevMode()) {\n    return liffDevMock.isLoggedIn();\n  }\n\n  try {\n    // 確保 LIFF 已初始化\n    if (!liff) {\n      console.warn('LIFF is not initialized yet');\n      return false;\n    }\n    return liff.isLoggedIn();\n  } catch (error) {\n    console.error('Error checking login status', error);\n    return false;\n  }\n};\n\n// 獲取用戶資料\nexport const getUserProfile = async () => {\n  try {\n    // 開發模式\n    if (isDevMode()) {\n      if (!liffDevMock.isLoggedIn()) {\n        throw new Error('User is not logged in');\n      }\n      const profile = await liffDevMock.getProfile();\n      return {\n        userId: profile.userId,\n        displayName: profile.displayName,\n        pictureUrl: profile.pictureUrl,\n      };\n    }\n\n    if (!liff.isLoggedIn()) {\n      throw new Error('User is not logged in');\n    }\n\n    // 獲取用戶資料\n    const profile = await liff.getProfile();\n    return {\n      userId: profile.userId,\n      displayName: profile.displayName,\n      pictureUrl: profile.pictureUrl,\n    };\n  } catch (error) {\n    console.error('Failed to get user profile', error);\n    throw error;\n  }\n};\n\n// LINE 登入\nexport const login = (): void => {\n  if (!liff.isLoggedIn()) {\n    liff.login();\n  }\n};\n\n// 完整的 LINE 登入流程（包含後端 API 調用）\nexport const loginWithBackend = async (userIndex = 0) => {\n  try {\n    // 開發模式：使用專用的開發登入流程\n    if (isDevMode()) {\n      return await devLoginWithBackend(userIndex);\n    }\n\n    if (!liff.isLoggedIn()) {\n      throw new Error('User is not logged in to LINE');\n    }\n\n    // 取得 access token\n    const accessToken = getAccessToken();\n    if (!accessToken) {\n      throw new Error('Unable to get access token');\n    }\n\n    // 調用後端 API 進行登入\n    const response = await apiService.auth.lineLogin(accessToken);\n\n    console.log('Backend login successful:', response);\n    return response;\n  } catch (error) {\n    console.error('Backend login failed:', error);\n    throw error;\n  }\n};\n\n// 清理用戶相關的本地資料\nexport const clearUserData = (): void => {\n  try {\n    // 清理 localStorage 中的用戶相關資料\n    const keysToRemove = [\n      'userProfile',\n      'userPreferences',\n      'cartItems',\n      'lastOrderId',\n      'userSettings'\n    ];\n\n    keysToRemove.forEach(key => {\n      localStorage.removeItem(key);\n    });\n\n    // 清理 sessionStorage\n    sessionStorage.clear();\n\n    console.log('User data cleared successfully');\n  } catch (error) {\n    console.error('Error clearing user data:', error);\n  }\n};\n\n// 清理所有認證相關的 token 和資料\nexport const clearAuthTokens = (): void => {\n  try {\n    // 清理可能儲存在 localStorage 中的 token\n    const tokenKeys = [\n      'access_token',\n      'refresh_token',\n      'id_token',\n      'liff_token',\n      'auth_token'\n    ];\n\n    tokenKeys.forEach(key => {\n      localStorage.removeItem(key);\n      sessionStorage.removeItem(key);\n    });\n\n    console.log('Auth tokens cleared successfully');\n  } catch (error) {\n    console.error('Error clearing auth tokens:', error);\n  }\n};\n\n// LINE 登出（基本版本，保持向後兼容）\nexport const logout = (): void => {\n  if (liff.isLoggedIn()) {\n    liff.logout();\n    window.location.reload();\n  }\n};\n\n// LINE 登出（完整版本，包含資料清理和 API 調用）\nexport const logoutWithCleanup = async (): Promise<void> => {\n  try {\n    if (!liff.isLoggedIn()) {\n      console.warn('User is not logged in');\n      return;\n    }\n\n    // 1. 先調用伺服器端登出 API（如果有 token）\n    try {\n      const token = getAccessToken();\n      if (token) {\n        await apiService.auth.logout();\n        console.log('Server logout successful');\n      }\n    } catch (apiError) {\n      console.warn('Server logout failed, continuing with client logout:', apiError);\n      // 即使伺服器登出失敗，也繼續執行客戶端登出\n    }\n\n    // 2. 清理本地資料和 token\n    clearUserData();\n    clearAuthTokens();\n\n    // 3. 執行 LIFF 登出（這會清除 LIFF 的 access token）\n    liff.logout();\n\n    // 4. 短暫延遲後重新載入頁面，確保登出完成\n    setTimeout(() => {\n      window.location.href = '/';\n    }, 100);\n\n  } catch (error) {\n    console.error('Logout failed:', error);\n    // 即使出錯也嘗試重新載入頁面\n    window.location.reload();\n  }\n};\n\n// 關閉 LIFF 視窗\nexport const closeLiff = (): void => {\n  liff.closeWindow();\n};\n\n// 獲取 LINE 用戶的 ID Token\nexport const getIdToken = (): string | null => {\n  if (typeof window === 'undefined') return null;\n  if (!liff.isLoggedIn()) {\n    return null;\n  }\n  return liff.getIDToken() || null;\n};\n\n// 獲取 LINE 用戶的 Access Token\nexport const getAccessToken = (): string | null => {\n  if (typeof window === 'undefined') return null;\n\n  // 開發模式\n  if (isDevMode()) {\n    return liffDevMock.getAccessToken();\n  }\n\n  if (!liff.isLoggedIn()) {\n    return null;\n  }\n  return liff.getAccessToken() || null;\n};\n\n// 打開外部連結\nexport const openExternalLink = (url: string): void => {\n  liff.openWindow({\n    url,\n    external: true,\n  });\n};\n\n// 分享訊息\nexport const shareMessage = async (messages: any[]): Promise<void> => {\n  if (!liff.isInClient()) {\n    console.error('Share message is only available in LINE app');\n    return;\n  }\n\n  try {\n    await liff.shareTargetPicker(messages);\n  } catch (error) {\n    console.error('Failed to share message', error);\n    throw error;\n  }\n};\n\nexport default {\n  initializeLiff,\n  isInLiffBrowser,\n  isLoggedIn,\n  getUserProfile,\n  login,\n  loginWithBackend,\n  logout,\n  logoutWithCleanup,\n  clearUserData,\n  clearAuthTokens,\n  closeLiff,\n  getIdToken,\n  getAccessToken,\n  openExternalLink,\n  shareMessage,\n};\n\n\n\n\n\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;;;;AAEA,UAAU;AACV,MAAM,UAAU,6EAEZ,uBAAwB,OAAO;AAEnC,YAAY;AACZ,MAAM,YAAY;IAChB,OAAO,oDAAyB,iBACzB,gBAAkB,eAClB,OAAO,QAAQ,CAAC,QAAQ,KAAK;AACtC;AAGO,MAAM,iBAAiB;IAC5B,IAAI;QACF,aAAa;QACb,IAAI,aAAa;;QAIjB;QAEA,uCAAc;;QAEd;QAEA,MAAM,wIAAA,CAAA,UAAI,CAAC,IAAI,CAAC;YACd,QAAQ;YACR,4BAA4B;QAC9B;QACA,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAGO,MAAM,kBAAkB;IAC7B,wCAAmC,OAAO;;AAQ5C;AAGO,MAAM,aAAa;IACxB,wCAAmC,OAAO;;AAkB5C;AAGO,MAAM,iBAAiB;IAC5B,IAAI;QACF,OAAO;QACP,IAAI,aAAa;;QAUjB;QAEA,IAAI,CAAC,wIAAA,CAAA,UAAI,CAAC,UAAU,IAAI;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,SAAS;QACT,MAAM,UAAU,MAAM,wIAAA,CAAA,UAAI,CAAC,UAAU;QACrC,OAAO;YACL,QAAQ,QAAQ,MAAM;YACtB,aAAa,QAAQ,WAAW;YAChC,YAAY,QAAQ,UAAU;QAChC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAGO,MAAM,QAAQ;IACnB,IAAI,CAAC,wIAAA,CAAA,UAAI,CAAC,UAAU,IAAI;QACtB,wIAAA,CAAA,UAAI,CAAC,KAAK;IACZ;AACF;AAGO,MAAM,mBAAmB,OAAO,YAAY,CAAC;IAClD,IAAI;QACF,mBAAmB;QACnB,IAAI,aAAa;;QAEjB;QAEA,IAAI,CAAC,wIAAA,CAAA,UAAI,CAAC,UAAU,IAAI;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,kBAAkB;QAClB,MAAM,cAAc;QACpB,IAAI,CAAC,aAAa;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,gBAAgB;QAChB,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC;QAEjD,QAAQ,GAAG,CAAC,6BAA6B;QACzC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAGO,MAAM,gBAAgB;IAC3B,IAAI;QACF,2BAA2B;QAC3B,MAAM,eAAe;YACnB;YACA;YACA;YACA;YACA;SACD;QAED,aAAa,OAAO,CAAC,CAAA;YACnB,aAAa,UAAU,CAAC;QAC1B;QAEA,oBAAoB;QACpB,eAAe,KAAK;QAEpB,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;IAC7C;AACF;AAGO,MAAM,kBAAkB;IAC7B,IAAI;QACF,gCAAgC;QAChC,MAAM,YAAY;YAChB;YACA;YACA;YACA;YACA;SACD;QAED,UAAU,OAAO,CAAC,CAAA;YAChB,aAAa,UAAU,CAAC;YACxB,eAAe,UAAU,CAAC;QAC5B;QAEA,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;IAC/C;AACF;AAGO,MAAM,SAAS;IACpB,IAAI,wIAAA,CAAA,UAAI,CAAC,UAAU,IAAI;QACrB,wIAAA,CAAA,UAAI,CAAC,MAAM;QACX,OAAO,QAAQ,CAAC,MAAM;IACxB;AACF;AAGO,MAAM,oBAAoB;IAC/B,IAAI;QACF,IAAI,CAAC,wIAAA,CAAA,UAAI,CAAC,UAAU,IAAI;YACtB,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,8BAA8B;QAC9B,IAAI;YACF,MAAM,QAAQ;YACd,IAAI,OAAO;gBACT,MAAM,mHAAA,CAAA,UAAU,CAAC,IAAI,CAAC,MAAM;gBAC5B,QAAQ,GAAG,CAAC;YACd;QACF,EAAE,OAAO,UAAU;YACjB,QAAQ,IAAI,CAAC,wDAAwD;QACrE,uBAAuB;QACzB;QAEA,mBAAmB;QACnB;QACA;QAEA,0CAA0C;QAC1C,wIAAA,CAAA,UAAI,CAAC,MAAM;QAEX,wBAAwB;QACxB,WAAW;YACT,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB,GAAG;IAEL,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kBAAkB;QAChC,gBAAgB;QAChB,OAAO,QAAQ,CAAC,MAAM;IACxB;AACF;AAGO,MAAM,YAAY;IACvB,wIAAA,CAAA,UAAI,CAAC,WAAW;AAClB;AAGO,MAAM,aAAa;IACxB,wCAAmC,OAAO;;AAK5C;AAGO,MAAM,iBAAiB;IAC5B,wCAAmC,OAAO;;AAW5C;AAGO,MAAM,mBAAmB,CAAC;IAC/B,wIAAA,CAAA,UAAI,CAAC,UAAU,CAAC;QACd;QACA,UAAU;IACZ;AACF;AAGO,MAAM,eAAe,OAAO;IACjC,IAAI,CAAC,wIAAA,CAAA,UAAI,CAAC,UAAU,IAAI;QACtB,QAAQ,KAAK,CAAC;QACd;IACF;IAEA,IAAI;QACF,MAAM,wIAAA,CAAA,UAAI,CAAC,iBAAiB,CAAC;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;uCAEe;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 680, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/0_project/augment-projects/cocodrink-line/client/src/providers/LiffProvider.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport liff from '@line/liff';\nimport { initializeLiff, isLoggedIn, getUserProfile } from '@/utils/liff';\n\n// LIFF 上下文類型\ninterface LiffContextType {\n  isInitialized: boolean;\n  isLoggedIn: boolean;\n  isInClient: boolean;\n  userProfile: any | null;\n  error: Error | null;\n  isDevelopmentMode?: boolean; // 新增開發模式標記\n}\n\n// 創建 LIFF 上下文\nconst LiffContext = createContext<LiffContextType>({\n  isInitialized: false,\n  isLoggedIn: false,\n  isInClient: false,\n  userProfile: null,\n  error: null,\n  isDevelopmentMode: false,\n});\n\n// 開發模式的模擬用戶資料\nconst mockUserProfile = {\n  userId: 'Udf723d52cde2495367205e5751fb8c8d',\n  displayName: 'Andy瑄',\n  pictureUrl: 'https://cdn-icons-png.flaticon.com/512/3135/3135715.png',\n};\n\n// LIFF 提供者組件\nexport const LiffProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  // 判斷是否為開發環境\n  const isDevelopment = process.env.NODE_ENV === 'development';\n  const [isInitialized, setIsInitialized] = useState(isDevelopment);\n  const [isInClient, setIsInClient] = useState(false);\n  const [userProfile, setUserProfile] = useState<any | null>(isDevelopment ? mockUserProfile : null);\n  const [error, setError] = useState<Error | null>(null);\n\n  useEffect(() => {\n    // 如果是開發模式且想要繞過 LIFF 登入，直接返回\n    if (isDevelopment && process.env.NEXT_PUBLIC_SKIP_LIFF_LOGIN === 'true') {\n      console.log('開發模式：已繞過 LIFF 登入');\n      setIsInitialized(true);\n      return;\n    }\n\n    // 確保只在客戶端執行 LIFF 初始化\n    if (typeof window !== 'undefined') {\n      // 初始化 LIFF\n      const initLiff = async () => {\n        try {\n          await initializeLiff();\n          setIsInitialized(true);\n\n          // 檢查是否在 LINE 應用內\n          const inClient = liff.isInClient();\n          console.log(\"inClient\", inClient);\n          setIsInClient(inClient);\n          console.log(\"isLoggedIn\", liff.isLoggedIn());\n          // 如果用戶已登入，獲取用戶資料\n          if (liff.isLoggedIn()) {\n            const profile = await getUserProfile();\n            console.log(\"profile\", profile);\n            setUserProfile(profile);\n          }\n        } catch (err) {\n          console.error('LIFF initialization failed', err);\n          setError(err instanceof Error ? err : new Error('Failed to initialize LIFF'));\n        }\n      };\n\n      initLiff();\n    }\n  }, [isDevelopment]);\n\n  // 提供 LIFF 上下文\n  const value = {\n    isInitialized,\n    isLoggedIn: isDevelopment && process.env.NEXT_PUBLIC_SKIP_LIFF_LOGIN === 'true' \n      ? true \n      : (typeof window !== 'undefined' ? isLoggedIn() : false),\n    isInClient,\n    userProfile,\n    error,\n    isDevelopmentMode: isDevelopment && process.env.NEXT_PUBLIC_SKIP_LIFF_LOGIN === 'true',\n  };\n\n  return <LiffContext.Provider value={value}>{children}</LiffContext.Provider>;\n};\n\n// 使用 LIFF 上下文的 Hook\nexport const useLiff = () => useContext(LiffContext);\n\nexport default LiffProvider;\n\n\n\n\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AAJA;;;;;AAgBA,cAAc;AACd,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAmB;IACjD,eAAe;IACf,YAAY;IACZ,YAAY;IACZ,aAAa;IACb,OAAO;IACP,mBAAmB;AACrB;AAEA,cAAc;AACd,MAAM,kBAAkB;IACtB,QAAQ;IACR,aAAa;IACb,YAAY;AACd;AAGO,MAAM,eAAwD,CAAC,EAAE,QAAQ,EAAE;IAChF,YAAY;IACZ,MAAM,gBAAgB,oDAAyB;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,uCAAgB;IAC3E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,4BAA4B;QAC5B,wCAAyE;YACvE,QAAQ,GAAG,CAAC;YACZ,iBAAiB;YACjB;QACF;;IA6BF,GAAG;QAAC;KAAc;IAElB,cAAc;IACd,MAAM,QAAQ;QACZ;QACA,YAAY,uCACR;QAEJ;QACA;QACA;QACA,mBAAmB,iBAAiB,6CAA4C;IAClF;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;AAGO,MAAM,UAAU,IAAM,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;uCAEzB", "debugId": null}}]}