(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[571],{3311:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},6128:(e,s,a)=>{Promise.resolve().then(a.bind(a,8679))},8679:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>N});var t=a(5155),l=a(9053),r=a(4222),i=a(6874),n=a.n(i),c=a(7312),d=a(7550),o=a(7809),x=a(9946);let h=(0,x.A)("flame",[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]]);var m=a(3311);let g=(0,x.A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);var p=a(4616),u=a(2115),j=a(2302),f=a(3841),b=a(1352);function N(){let{userProfile:e}=(0,r.x)(),{addToCart:s,getTotalQuantity:a,getTotalPrice:i}=(0,f._)(),[x,N]=(0,u.useState)("all"),[w,v]=(0,u.useState)([]),[y,A]=(0,u.useState)([]),[k,C]=(0,u.useState)(!0),[z,q]=(0,u.useState)(null),[M,S]=(0,u.useState)(null),[_,L]=(0,u.useState)(!1),E=async()=>{try{let e=await j.A.products.getCategories();v(["all",...e])}catch(e){console.error("Failed to fetch categories:",e),q("無法載入產品分類")}},D=async()=>{try{let e=await j.A.products.getAll();A(e)}catch(e){console.error("Failed to fetch products:",e),q("無法載入產品列表")}finally{C(!1)}};(0,u.useEffect)(()=>{(async()=>{C(!0),await Promise.all([E(),D()])})()},[]);let O=e=>{S(e),L(!0)},P=e=>{e.options&&e.options.length>0?O(e):s(e)},T="all"===x?y:y.filter(e=>e.category===x);return k?(0,t.jsx)(l.A,{requireAuth:!0,requireVerification:!0,children:(0,t.jsx)("div",{className:"min-h-screen gradient-bg flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"載入產品中..."})]})})}):z?(0,t.jsx)(l.A,{requireAuth:!0,requireVerification:!0,children:(0,t.jsx)("div",{className:"min-h-screen gradient-bg flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(c.A,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-red-500 mb-4",children:z}),(0,t.jsx)("button",{onClick:()=>window.location.reload(),className:"px-6 py-3 bg-orange-500 text-white rounded-full hover:bg-orange-600 transition-colors",children:"重新載入"})]})})}):(0,t.jsx)(l.A,{requireAuth:!0,requireVerification:!0,children:(0,t.jsxs)("div",{className:"min-h-screen gradient-bg",children:[(0,t.jsx)("header",{className:"bg-white/80 backdrop-blur-md shadow-soft sticky top-0 z-50",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(n(),{href:"/",className:"p-2 bg-orange-100 text-orange-600 rounded-full hover:bg-orange-200 transition-colors",children:(0,t.jsx)(d.A,{className:"w-5 h-5"})}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(c.A,{className:"w-6 h-6 text-orange-500"}),(0,t.jsx)("h1",{className:"text-xl font-bold text-gray-800",children:"點餐"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:null==e?void 0:e.displayName}),(0,t.jsxs)(n(),{href:"/cart",className:"relative",children:[(0,t.jsx)("div",{className:"p-2 bg-green-100 text-green-600 rounded-full hover:bg-green-200 transition-colors",children:(0,t.jsx)(o.A,{className:"w-5 h-5"})}),a()>0&&(0,t.jsx)("div",{className:"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center",children:a()})]})]})]})}),(0,t.jsxs)("main",{className:"container mx-auto px-4 py-6",children:[(0,t.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm p-6 mb-6",children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:["歡迎點餐！",null==e?void 0:e.displayName," \uD83D\uDC4B"]}),(0,t.jsx)("p",{className:"text-gray-600",children:"選擇您喜愛的飲品，我們為您精心調製"})]}),(0,t.jsx)("div",{className:"sticky top-[73px] z-40 bg-gradient-to-b from-orange-50 to-transparent pb-4 mb-6",children:(0,t.jsx)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-soft",children:(0,t.jsx)("div",{className:"flex gap-2 overflow-x-auto pb-2",children:w.map(e=>(0,t.jsx)("button",{onClick:()=>N(e),className:"px-4 py-2 rounded-full whitespace-nowrap transition-colors shadow-sm ".concat(x===e?"bg-orange-500 text-white shadow-orange-200":"bg-white text-gray-600 hover:bg-orange-100 border border-gray-200"),children:"all"===e?"全部":e},e))})})}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pb-32",children:T.map(e=>(0,t.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm overflow-hidden hover:shadow-lg transition-shadow relative",children:[(0,t.jsxs)("div",{className:"absolute top-3 left-3 z-10 flex gap-2",children:[e.isPopular&&(0,t.jsxs)("div",{className:"flex items-center gap-1 px-2 py-1 bg-red-500 text-white text-xs font-bold rounded-full shadow-lg",children:[(0,t.jsx)(h,{className:"w-3 h-3"}),"熱門"]}),e.isNew&&(0,t.jsxs)("div",{className:"flex items-center gap-1 px-2 py-1 bg-green-500 text-white text-xs font-bold rounded-full shadow-lg",children:[(0,t.jsx)(m.A,{className:"w-3 h-3"}),"新品"]})]}),(0,t.jsx)("div",{className:"h-48 bg-gradient-to-br from-orange-100 to-orange-200 flex items-center justify-center",children:e.image?(0,t.jsx)("img",{src:e.image,alt:e.name,className:"w-full h-full object-cover"}):(0,t.jsx)(c.A,{className:"w-16 h-16 text-orange-400"})}),(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("h3",{className:"font-bold text-gray-800",children:e.name}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(g,{className:"w-4 h-4 text-yellow-400 fill-current"}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"4.5"})]})]}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:e.description||"美味飲品，值得品嚐"}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("span",{className:"text-xl font-bold text-orange-500",children:["NT$ ",e.price]}),(0,t.jsxs)("button",{onClick:()=>P(e),className:"flex items-center gap-2 px-4 py-2 bg-orange-500 text-white rounded-full hover:bg-orange-600 transition-colors",children:[(0,t.jsx)(p.A,{className:"w-4 h-4"}),e.options&&e.options.length>0?"客製化":"加入購物車"]})]})]})]},e.id))}),0===T.length&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(c.A,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-500",children:"此分類暫無商品"})]})]}),a()>0&&(0,t.jsx)("div",{className:"fixed bottom-0 left-0 right-0 z-50 p-4 bg-gradient-to-t from-white via-white to-transparent",children:(0,t.jsx)("div",{className:"container mx-auto max-w-md",children:(0,t.jsxs)(n(),{href:"/cart",className:"flex items-center justify-between w-full py-4 px-6 gradient-green text-white rounded-2xl shadow-warm btn-hover",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(o.A,{className:"w-6 h-6"}),(0,t.jsx)("div",{className:"absolute -top-2 -right-2 w-5 h-5 bg-white text-green-600 text-xs rounded-full flex items-center justify-center font-bold",children:a()})]}),(0,t.jsx)("span",{className:"font-semibold",children:"查看購物車"})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("p",{className:"text-sm opacity-90",children:"總計"}),(0,t.jsxs)("p",{className:"font-bold text-lg",children:["NT$ ",i()]})]})]})})}),M&&(0,t.jsx)(b.A,{product:M,isOpen:_,onClose:()=>{L(!1),S(null)},onAddToCart:(e,a,t)=>{s(e,a,t),L(!1),S(null)}})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[932,659,222,142,441,684,358],()=>s(6128)),_N_E=e.O()}]);